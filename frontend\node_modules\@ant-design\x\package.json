{"name": "@ant-design/x", "version": "1.5.0", "description": "Craft AI-driven interfaces effortlessly", "keywords": ["AI", "Copilot", "ant", "components", "framework", "react"], "homepage": "https://x.ant.design", "bugs": {"url": "https://github.com/ant-design/x/issues"}, "repository": {"type": "git", "url": "https://github.com/ant-design/x"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/ant-design"}, "license": "MIT", "sideEffects": false, "main": "lib/index.js", "module": "es/index.js", "typings": "es/index.d.ts", "files": ["dist", "es", "lib", "locale", "BUG_VERSIONS.json"], "scripts": {"api-collection": "antd-tools run api-collection", "authors": "tsx scripts/generate-authors.ts", "changelog": "npm run lint:changelog && tsx scripts/print-changelog.ts", "check-commit": "tsx scripts/check-commit.ts", "clean": "antd-tools run clean && rm -rf es lib coverage locale dist report.html artifacts.zip oss-artifacts.zip", "precompile": "npm run prestart", "compile": "father build", "predeploy": "npm run site && cp CNAME _site", "deploy": "gh-pages -d _site -b gh-pages -f", "predist": "npm run version && npm run token:statistic && npm run token:meta", "format": "biome format --write .", "prelint": "dumi setup", "lint": "npm run version && npm run tsc && npm run lint:script && npm run lint:md && npm run lint:style && npm run lint:changelog", "lint:changelog": "tsx scripts/generate-component-changelog.ts", "lint:deps": "antd-tools run deps-lint", "lint:md": "remark . -f -q", "lint:script": "biome lint", "lint:style": "tsx scripts/check-cssinjs.tsx", "prepare": "husky", "prepublishOnly": "tsx ./scripts/pre-publish.ts", "prettier": "prettier -c --write **/* --cache", "pub": "echo 'Please use `npm publish` instead.'", "presite": "npm run prestart", "site": "dumi build && cp .surgeignore _site", "size-limit": "size-limit", "sort:api-table": "antd-tools run sort-api-table", "sort:package-json": "npx sort-package-json", "prestart": "npm run version && npm run token:statistic && npm run token:meta && npm run lint:changelog", "start": "tsx ./scripts/set-node-options.ts cross-env PORT=8001 dumi dev", "pretest": "npm run version", "test": "jest --config .jest.js --no-cache --collect-coverage", "test:dekko": "tsx ./tests/dekko/index.test.ts", "test:node": "npm run version && jest --config .jest.node.js --no-cache", "test:package-diff": "antd-tools run package-diff", "test:site": "jest --config .jest.site.js", "test:visual-regression": "tsx scripts/visual-regression/build.ts", "token:meta": "tsx scripts/generate-token-meta.ts", "token:statistic": "tsx scripts/collect-token-statistic.ts", "tsc": "tsc --noEmit", "version": "tsx scripts/generate-version.ts"}, "browserslist": ["defaults"], "dependencies": {"@ant-design/colors": "^7.1.0", "@ant-design/cssinjs": "^1.21.1", "@ant-design/cssinjs-utils": "^1.1.0", "@ant-design/fast-color": "^2.0.6", "@ant-design/icons": "^5.4.0", "@babel/runtime": "^7.25.6", "classnames": "^2.5.1", "rc-motion": "^2.9.2", "rc-util": "^5.43.0"}, "devDependencies": {"@ant-design/tools": "^19.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@antv/gpt-vis": "^0.4.1", "@biomejs/biome": "^2.0.5", "@codecov/webpack-plugin": "^1.4.0", "@codesandbox/sandpack-react": "^2.19.8", "@emotion/react": "^11.13.5", "@emotion/server": "^11.11.0", "@happy-dom/jest-environment": "^18.0.1", "@ianvs/prettier-plugin-sort-imports": "^4.3.1", "@madccc/duplicate-package-checker-webpack-plugin": "^1.0.0", "@microflash/rehype-figure": "^2.1.1", "@npmcli/run-script": "^9.0.1", "@octokit/rest": "^21.0.2", "@petercatai/assistant": "^2.0.13", "@prettier/sync": "^0.6.1", "@qixian.cs/github-contributors-list": "^2.0.2", "@rc-component/father-plugin": "^2.0.4", "@rc-component/np": "^1.0.3", "@rc-component/trigger": "^2.2.3", "@size-limit/file": "^11.1.5", "@stackblitz/sdk": "^1.11.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/adm-zip": "^0.5.5", "@types/ali-oss": "^6.16.11", "@types/cli-progress": "^3.11.6", "@types/fs-extra": "^11.0.4", "@types/gtag.js": "^0.0.20", "@types/http-server": "^0.12.4", "@types/inquirer": "^9.0.7", "@types/isomorphic-fetch": "^0.0.39", "@types/jest": "^29.5.13", "@types/jest-axe": "^3.5.9", "@types/jest-environment-puppeteer": "^5.0.6", "@types/jest-image-snapshot": "^6.4.0", "@types/jquery": "^3.5.30", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.7", "@types/markdown-it": "^14.1.2", "@types/minimist": "^1.2.5", "@types/node": "^24.0.3", "@types/ora": "^3.2.0", "@types/pixelmatch": "^5.2.6", "@types/pngjs": "^6.0.5", "@types/prismjs": "^1.26.4", "@types/progress": "^2.0.7", "@types/qs": "^6.9.16", "@types/react": "^19.0.2", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^19.0.2", "@types/react-highlight-words": "^0.20.0", "@types/react-resizable": "^3.0.8", "@types/semver": "^7.5.8", "@types/spinnies": "^0.5.3", "@types/tar": "^6.1.13", "@types/throttle-debounce": "^5.0.2", "@types/warning": "^3.0.3", "@umijs/fabric": "^4.0.1", "adm-zip": "^0.5.16", "ali-oss": "^6.21.0", "antd": "^5.25.1", "antd-style": "^3.6.3", "antd-token-previewer": "^2.0.8", "axios": "^1.7.7", "browserslist": "^4.23.3", "browserslist-to-esbuild": "^2.1.1", "chalk": "^5.4.1", "cheerio": "^1.0.0", "circular-dependency-plugin": "^5.2.2", "cli-progress": "^3.12.0", "copy-to-clipboard": "^3.3.3", "cross-env": "^7.0.3", "cross-fetch": "^4.0.0", "crypto": "^1.0.1", "dayjs": "^1.11.13", "dekko": "^0.2.1", "dumi": "^2.4.17", "dumi-plugin-color-chunk": "^1.1.2", "esbuild-loader": "^4.2.2", "fast-glob": "^3.3.2", "father": "^4.5.4", "fetch-jsonp": "^1.3.0", "fs-extra": "^11.2.0", "gh-pages": "^6.1.1", "glob": "^11.0.0", "happy-dom": "^18.0.1", "html2sketch": "^1.0.2", "http-server": "^14.1.1", "husky": "^9.1.6", "identity-obj-proxy": "^3.0.0", "immer": "^10.1.1", "inquirer": "^12.1.0", "is-ci": "^4.1.0", "isomorphic-fetch": "^3.0.0", "jest": "^30.0.3", "jest-axe": "^10.0.0", "jest-canvas-mock": "^2.5.2", "jest-environment-node": "^30.0.0", "jest-image-snapshot": "^6.4.0", "jest-puppeteer": "^11.0.0", "jquery": "^3.7.1", "jsdom": "^26.0.0", "jsonml-to-react-element": "^1.1.11", "jsonml.js": "^0.1.0", "lint-staged": "^16.1.2", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "lunar-typescript": "^1.7.5", "lz-string": "^1.5.0", "markdown-it": "^14.1.0", "minimist": "^1.2.8", "mockdate": "^3.0.5", "node-fetch": "^3.3.2", "node-notifier": "^10.0.1", "open": "^10.1.0", "ora": "^8.1.0", "pixelmatch": "^7.1.0", "pngjs": "^7.0.0", "prettier": "^3.3.3", "prettier-plugin-jsdoc": "^1.3.0", "pretty-format": "^30.0.0", "prismjs": "^1.29.0", "puppeteer": "^24.0.0", "qs": "^6.13.0", "rc-drawer": "^8.0.0", "rc-footer": "^0.6.8", "rc-resize-observer": "^1.4.0", "rc-virtual-list": "^3.14.5", "react": "^19.0.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^19.0.0", "react-highlight-words": "^0.20.0", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.13.1", "react-intl": "^7.1.11", "react-resizable": "^3.0.5", "react-router-dom": "^7.0.1", "react-sticky-box": "^2.0.5", "regenerator-runtime": "^0.14.1", "rehype-stringify": "^10.0.0", "remark": "^15.0.1", "remark-cli": "^12.0.1", "remark-gfm": "^4.0.0", "remark-lint": "^10.0.0", "remark-lint-no-undefined-references": "^5.0.0", "remark-preset-lint-recommended": "^7.0.0", "remark-rehype": "^11.1.0", "scroll-into-view-if-needed": "^3.1.0", "semver": "^7.6.3", "sharp": "^0.34.3", "simple-git": "^3.26.0", "size-limit": "^11.1.5", "spinnies": "^0.5.1", "tar": "^7.4.3", "tsx": "^4.19.1", "typedoc": "^0.28.0", "typescript": "~5.8.2", "vanilla-jsoneditor": "^3.0.0", "web-streams-polyfill": "^4.0.0", "webpack": "^5.94.0", "webpack-bundle-analyzer": "^4.10.2"}, "peerDependencies": {"antd": "^5.20.3", "react": ">=18.0.0", "react-dom": ">=18.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "size-limit": [{"path": "./dist/antdx.min.js", "limit": "350 KiB"}]}