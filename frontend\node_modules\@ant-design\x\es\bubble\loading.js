import React from 'react';
const Loading = ({
  prefixCls
}) => /*#__PURE__*/React.createElement("span", {
  className: `${prefixCls}-dot`
}, /*#__PURE__*/React.createElement("i", {
  className: `${prefixCls}-dot-item`,
  key: `item-${1}`
}), /*#__PURE__*/React.createElement("i", {
  className: `${prefixCls}-dot-item`,
  key: `item-${2}`
}), /*#__PURE__*/React.createElement("i", {
  className: `${prefixCls}-dot-item`,
  key: `item-${3}`
}));
export default Loading;