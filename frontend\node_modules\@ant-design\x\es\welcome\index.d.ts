import React from 'react';
export type SemanticType = 'title' | 'description' | 'icon' | 'extra';
export interface WelcomeProps {
    prefixCls?: string;
    rootClassName?: string;
    className?: string;
    style?: React.CSSProperties;
    variant?: 'filled' | 'borderless';
    classNames?: Partial<Record<SemanticType, string>>;
    styles?: Partial<Record<SemanticType, React.CSSProperties>>;
    icon?: React.ReactNode;
    title?: React.ReactNode;
    description?: React.ReactNode;
    extra?: React.ReactNode;
}
declare const ForwardWelcome: React.ForwardRefExoticComponent<WelcomeProps & React.RefAttributes<HTMLDivElement>>;
export default ForwardWelcome;
