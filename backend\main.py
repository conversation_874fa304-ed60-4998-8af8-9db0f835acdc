import asyncio
import json
from datetime import datetime
from typing import AsyncGenerator, Dict, Any, Optional, List

from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from starlette.responses import JSONResponse

from test_case_service import TestCaseService
from template_service import TemplateService
from project_service import ProjectService

app = FastAPI(title="黑湖科技智能自动化测试平台 API", version="1.0.0", description="黑湖科技智能自动化测试平台（100%自研），支持页面截图分析和AI驱动的测试用例生成")

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化服务
test_case_service = TestCaseService()
template_service = TemplateService()
project_service = ProjectService()

# ==================== 数据模型 ====================

class TestCaseGenerateRequest(BaseModel):
    project_id: str
    requirement_text: str
    template_id: Optional[str] = None
    generation_type: str = "functional"  # functional, performance, security, api
    include_negative_cases: bool = True
    include_boundary_cases: bool = True
    include_performance_cases: bool = False
    page_images: Optional[List[str]] = None  # base64编码的图片数据
    ui_descriptions: Optional[List[str]] = None  # UI元素描述

class TestCaseResponse(BaseModel):
    test_case_id: str
    title: str
    description: str
    preconditions: List[str]
    steps: List[Dict[str, str]]
    expected_result: str
    priority: str
    category: str
    tags: List[str]

class ProjectCreateRequest(BaseModel):
    name: str
    description: str
    project_type: str = "web"  # web, mobile, api, desktop
    
class TemplateCreateRequest(BaseModel):
    name: str
    description: str
    template_type: str
    content: Dict[str, Any]
    is_public: bool = True

# ==================== 基础接口 ====================

@app.get("/")
async def root():
    return {"message": "黑湖科技智能自动化测试平台 API 正在运行", "platform": "黑湖科技智能自动化测试平台（100%自研）", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# ==================== 测试用例生成接口 ====================

@app.post("/test-cases/generate/stream")
async def generate_test_cases_stream(request: TestCaseGenerateRequest):
    """流式生成测试用例接口"""
    
    async def generate_response() -> AsyncGenerator[str, None]:
        try:
            # 发送开始事件
            yield f"data: {json.dumps({'type': 'start', 'content': '开始生成测试用例...', 'finished': False})}\n\n"
            
            # 获取流式响应
            async for event in test_case_service.generate_test_cases_stream(request):
                event["finished"] = False
                yield f"data: {json.dumps(event)}\n\n"
            
            # 发送结束事件
            yield f"data: {json.dumps({'type': 'end', 'content': '测试用例生成完成', 'finished': True})}\n\n"
            
        except Exception as e:
            error_data = {
                "type": "error",
                "content": f"生成失败: {str(e)}",
                "finished": True
            }
            yield f"data: {json.dumps(error_data)}\n\n"
    
    return StreamingResponse(
        generate_response(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@app.post("/test-cases/generate")
async def generate_test_cases(request: TestCaseGenerateRequest):
    """非流式生成测试用例接口"""
    try:
        test_cases = await test_case_service.generate_test_cases(request)
        return {
            "status": "success",
            "message": "测试用例生成成功",
            "data": test_cases
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/test-cases/project/{project_id}")
async def get_project_test_cases(project_id: str):
    """获取项目的所有测试用例"""
    try:
        test_cases = test_case_service.get_project_test_cases(project_id)
        return {
            "status": "success",
            "project_id": project_id,
            "test_cases_count": len(test_cases),
            "test_cases": test_cases
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取测试用例失败: {str(e)}")

@app.get("/test-cases/{test_case_id}")
async def get_test_case(test_case_id: str):
    """获取单个测试用例详情"""
    try:
        test_case = test_case_service.get_test_case(test_case_id)
        if not test_case:
            raise HTTPException(status_code=404, detail="测试用例不存在")
        
        return {
            "status": "success",
            "test_case": test_case
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取测试用例失败: {str(e)}")

@app.put("/test-cases/{test_case_id}")
async def update_test_case(test_case_id: str, test_case_data: Dict[str, Any]):
    """更新测试用例"""
    try:
        updated_test_case = test_case_service.update_test_case(test_case_id, test_case_data)
        return {
            "status": "success",
            "message": "测试用例更新成功",
            "test_case": updated_test_case
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新测试用例失败: {str(e)}")

@app.delete("/test-cases/{test_case_id}")
async def delete_test_case(test_case_id: str):
    """删除测试用例"""
    try:
        success = test_case_service.delete_test_case(test_case_id)
        if not success:
            raise HTTPException(status_code=404, detail="测试用例不存在")
        
        return {
            "status": "success",
            "message": "测试用例删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除测试用例失败: {str(e)}")

# ==================== 项目管理接口 ====================

@app.post("/projects")
async def create_project(request: ProjectCreateRequest):
    """创建新项目"""
    try:
        project = project_service.create_project(request)
        return {
            "status": "success",
            "message": "项目创建成功",
            "project": project
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建项目失败: {str(e)}")

@app.get("/projects")
async def get_projects():
    """获取所有项目"""
    try:
        projects = project_service.get_all_projects()
        return {
            "status": "success",
            "projects_count": len(projects),
            "projects": projects
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")

@app.get("/projects/{project_id}")
async def get_project(project_id: str):
    """获取项目详情"""
    try:
        project = project_service.get_project(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        return {
            "status": "success",
            "project": project
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目失败: {str(e)}")

@app.put("/projects/{project_id}")
async def update_project(project_id: str, project_data: Dict[str, Any]):
    """更新项目"""
    try:
        updated_project = project_service.update_project(project_id, project_data)
        return {
            "status": "success",
            "message": "项目更新成功",
            "project": updated_project
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新项目失败: {str(e)}")

@app.delete("/projects/{project_id}")
async def delete_project(project_id: str):
    """删除项目"""
    try:
        success = project_service.delete_project(project_id)
        if not success:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        return {
            "status": "success",
            "message": "项目删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除项目失败: {str(e)}")

# ==================== 模板管理接口 ====================

@app.post("/templates")
async def create_template(request: TemplateCreateRequest):
    """创建新模板"""
    try:
        template = template_service.create_template(request)
        return {
            "status": "success",
            "message": "模板创建成功",
            "template": template
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建模板失败: {str(e)}")

@app.get("/templates")
async def get_templates(template_type: Optional[str] = None, public_only: bool = False):
    """获取模板列表"""
    try:
        if public_only:
            templates = template_service.get_public_templates()
        elif template_type:
            templates = template_service.get_templates_by_type(template_type)
        else:
            templates = template_service.get_all_templates()

        return {
            "status": "success",
            "templates_count": len(templates),
            "templates": templates
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")

@app.get("/templates/{template_id}")
async def get_template(template_id: str):
    """获取模板详情"""
    try:
        template = template_service.get_template(template_id)
        if not template:
            raise HTTPException(status_code=404, detail="模板不存在")

        # 增加使用次数
        template_service.increment_usage(template_id)

        return {
            "status": "success",
            "template": template
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板失败: {str(e)}")

@app.put("/templates/{template_id}")
async def update_template(template_id: str, template_data: Dict[str, Any]):
    """更新模板"""
    try:
        updated_template = template_service.update_template(template_id, template_data)
        return {
            "status": "success",
            "message": "模板更新成功",
            "template": updated_template
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新模板失败: {str(e)}")

@app.delete("/templates/{template_id}")
async def delete_template(template_id: str):
    """删除模板"""
    try:
        success = template_service.delete_template(template_id)
        if not success:
            raise HTTPException(status_code=404, detail="模板不存在")

        return {
            "status": "success",
            "message": "模板删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除模板失败: {str(e)}")

@app.get("/templates/stats/overview")
async def get_template_stats():
    """获取模板统计信息"""
    try:
        stats = template_service.get_template_stats()
        return {
            "status": "success",
            "stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板统计失败: {str(e)}")

# ==================== 统计和分析接口 ====================

@app.get("/stats/overview")
async def get_overview_stats():
    """获取系统概览统计"""
    try:
        project_stats = project_service.get_project_stats()
        template_stats = template_service.get_template_stats()

        return {
            "status": "success",
            "stats": {
                "projects": project_stats,
                "templates": template_stats,
                "system_info": {
                    "api_version": "1.0.0",
                    "last_updated": datetime.now().isoformat()
                }
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@app.get("/projects/search")
async def search_projects(keyword: str):
    """搜索项目"""
    try:
        projects = project_service.search_projects(keyword)
        return {
            "status": "success",
            "keyword": keyword,
            "results_count": len(projects),
            "projects": projects
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索项目失败: {str(e)}")

@app.get("/projects/recent")
async def get_recent_projects(limit: int = 5):
    """获取最近的项目"""
    try:
        projects = project_service.get_recent_projects(limit)
        return {
            "status": "success",
            "projects": projects
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取最近项目失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
