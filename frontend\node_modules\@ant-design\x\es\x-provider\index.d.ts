import React from 'react';
import useXProviderContext, { defaultPrefixCls } from './hooks/use-x-provider-context';
import type { ConfigProviderProps as AntdConfigProviderProps } from 'antd/es/config-provider';
import type { XProviderProps } from './context';
declare const XProvider: React.FC<XProviderProps & AntdConfigProviderProps>;
export { useXProviderContext, defaultPrefixCls };
export type { XProviderProps };
export default XProvider;
