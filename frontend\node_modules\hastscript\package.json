{"name": "hastscript", "version": "6.0.0", "description": "hast utility to create trees", "license": "MIT", "keywords": ["unist", "hast", "hast-util", "util", "utility", "html", "rehype", "vdom", "virtual", "dom", "hyperscript", "dsl"], "repository": "syntax-tree/hastscript", "bugs": "https://github.com/syntax-tree/hastscript/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "types": "index.d.ts", "files": ["index.d.ts", "svg.d.ts", "index.js", "factory.js", "html.js", "svg.js", "svg-case-sensitive-tag-names.json"], "dependencies": {"@types/hast": "^2.0.0", "comma-separated-tokens": "^1.0.0", "hast-util-parse-selector": "^2.0.0", "property-information": "^5.0.0", "space-separated-tokens": "^1.0.0"}, "devDependencies": {"browserify": "^16.0.0", "dtslint": "^3.0.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^8.0.0", "remark-preset-wooorm": "^7.0.0", "svg-tag-names": "^2.0.0", "tape": "^5.0.0", "tinyify": "^2.0.0", "xo": "^0.32.0"}, "scripts": {"generate": "node build", "format": "remark . -qfo && prettier . --write && xo --fix", "build-bundle": "browserify . -s hastscript > hastscript.js", "build-mangle": "browserify . -s hastscript -p tinyify > hastscript.min.js", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test.js", "test-types": "dtslint .", "test": "npm run generate && npm run format && npm run build && npm run test-coverage && npm run test-types"}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "rules": {"unicorn/prefer-number-properties": "off", "no-self-compare": "off", "guard-for-in": "off"}, "ignores": ["hastscript.js"]}, "remarkConfig": {"plugins": ["preset-wooorm"]}}