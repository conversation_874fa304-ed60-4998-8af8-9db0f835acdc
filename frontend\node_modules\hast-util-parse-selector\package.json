{"name": "hast-util-parse-selector", "version": "2.2.5", "description": "hast utility to create an element from a simple CSS selector", "license": "MIT", "keywords": ["unist", "hast", "hast-util", "util", "utility", "html", "css", "selector", "parse"], "repository": "syntax-tree/hast-util-parse-selector", "bugs": "https://github.com/syntax-tree/hast-util-parse-selector/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["index.js"], "dependencies": {}, "devDependencies": {"browserify": "^17.0.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "tinyify": "^3.0.0", "xo": "^0.34.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "build-bundle": "browserify . -s hastUtilParseSelector > hast-util-parse-selector.js", "build-mangle": "browserify . -s hastUtilParseSelector -p tinyify > hast-util-parse-selector.min.js", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test.js", "test": "npm run format && npm run build && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "ignores": ["hast-util-parse-selector.js"]}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "remarkConfig": {"plugins": ["preset-wooorm"]}}