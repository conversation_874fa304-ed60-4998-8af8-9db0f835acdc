// TypeScript Version: 3.5

import {Element, Properties, Node} from 'hast'

/**
 * DSL to create virtual hast trees for HTML or SVG
 *
 * @param selector Simple CSS selector
 * @param children (Lists of) child nodes
 */
declare function hastscript(
  selector?: string,
  children?: string | Node | Array<string | Node>
): Element

/**
 * DSL to create virtual hast trees for HTML or SVG
 *
 * @param selector Simple CSS selector
 * @param properties Map of properties
 * @param children (Lists of) child nodes
 */
declare function hastscript(
  selector?: string,
  properties?: Properties,
  children?: string | Node | Array<string | Node>
): Element

export = hastscript
