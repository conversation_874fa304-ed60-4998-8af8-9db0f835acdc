import _extends from "@babel/runtime/helpers/esm/extends";
import { ClearOutlined } from '@ant-design/icons';
import * as React from 'react';
import ActionButton from "./ActionButton";
function ClearButton(props, ref) {
  return /*#__PURE__*/React.createElement(ActionButton, _extends({
    icon: /*#__PURE__*/React.createElement(ClearOutlined, null)
  }, props, {
    action: "onClear",
    ref: ref
  }));
}
export default /*#__PURE__*/React.forwardRef(ClearButton);