import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

class ProjectService:
    def __init__(self):
        self.projects_db: Dict[str, Dict[str, Any]] = {}
        self._init_demo_projects()
    
    def _init_demo_projects(self):
        """初始化演示项目"""
        demo_projects = [
            {
                "name": "电商平台测试项目",
                "description": "针对电商平台的全面测试项目，包括用户管理、商品管理、订单处理等核心功能",
                "project_type": "web",
                "features": [
                    "用户注册登录",
                    "商品浏览搜索",
                    "购物车管理",
                    "订单处理",
                    "支付系统",
                    "用户评价"
                ]
            },
            {
                "name": "移动银行APP测试",
                "description": "移动银行应用的安全性和功能性测试项目",
                "project_type": "mobile",
                "features": [
                    "身份认证",
                    "账户查询",
                    "转账汇款",
                    "理财产品",
                    "安全设置",
                    "客服功能"
                ]
            },
            {
                "name": "API网关测试项目",
                "description": "微服务架构下API网关的接口测试项目",
                "project_type": "api",
                "features": [
                    "接口路由",
                    "认证授权",
                    "限流熔断",
                    "监控日志",
                    "版本管理",
                    "负载均衡"
                ]
            }
        ]
        
        for project_data in demo_projects:
            project_id = str(uuid.uuid4())
            project = {
                "project_id": project_id,
                "name": project_data["name"],
                "description": project_data["description"],
                "project_type": project_data["project_type"],
                "features": project_data["features"],
                "status": "active",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "test_cases_count": 0,
                "last_generation_time": None
            }
            self.projects_db[project_id] = project
    
    def create_project(self, request) -> Dict[str, Any]:
        """创建新项目"""
        project_id = str(uuid.uuid4())
        
        project = {
            "project_id": project_id,
            "name": request.name,
            "description": request.description,
            "project_type": request.project_type,
            "features": [],
            "status": "active",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "test_cases_count": 0,
            "last_generation_time": None
        }
        
        self.projects_db[project_id] = project
        return project
    
    def get_all_projects(self) -> List[Dict[str, Any]]:
        """获取所有项目"""
        return list(self.projects_db.values())
    
    def get_active_projects(self) -> List[Dict[str, Any]]:
        """获取活跃项目"""
        return [project for project in self.projects_db.values() if project["status"] == "active"]
    
    def get_projects_by_type(self, project_type: str) -> List[Dict[str, Any]]:
        """根据类型获取项目"""
        return [project for project in self.projects_db.values() 
                if project["project_type"] == project_type]
    
    def get_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """获取单个项目"""
        return self.projects_db.get(project_id)
    
    def update_project(self, project_id: str, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新项目"""
        if project_id not in self.projects_db:
            raise Exception("项目不存在")
        
        project = self.projects_db[project_id]
        
        # 更新允许的字段
        allowed_fields = ["name", "description", "project_type", "features", "status"]
        for field in allowed_fields:
            if field in project_data:
                project[field] = project_data[field]
        
        project["updated_at"] = datetime.now().isoformat()
        
        return project
    
    def delete_project(self, project_id: str) -> bool:
        """删除项目"""
        if project_id not in self.projects_db:
            return False
        
        del self.projects_db[project_id]
        return True
    
    def update_test_cases_count(self, project_id: str, count: int):
        """更新项目测试用例数量"""
        if project_id in self.projects_db:
            self.projects_db[project_id]["test_cases_count"] = count
            self.projects_db[project_id]["updated_at"] = datetime.now().isoformat()
    
    def update_last_generation_time(self, project_id: str):
        """更新最后生成时间"""
        if project_id in self.projects_db:
            self.projects_db[project_id]["last_generation_time"] = datetime.now().isoformat()
            self.projects_db[project_id]["updated_at"] = datetime.now().isoformat()
    
    def add_project_feature(self, project_id: str, feature: str) -> bool:
        """添加项目功能特性"""
        if project_id not in self.projects_db:
            return False
        
        project = self.projects_db[project_id]
        if "features" not in project:
            project["features"] = []
        
        if feature not in project["features"]:
            project["features"].append(feature)
            project["updated_at"] = datetime.now().isoformat()
        
        return True
    
    def remove_project_feature(self, project_id: str, feature: str) -> bool:
        """移除项目功能特性"""
        if project_id not in self.projects_db:
            return False
        
        project = self.projects_db[project_id]
        if "features" in project and feature in project["features"]:
            project["features"].remove(feature)
            project["updated_at"] = datetime.now().isoformat()
            return True
        
        return False
    
    def get_project_stats(self) -> Dict[str, Any]:
        """获取项目统计信息"""
        total_projects = len(self.projects_db)
        active_projects = len([p for p in self.projects_db.values() if p["status"] == "active"])
        
        type_stats = {}
        for project in self.projects_db.values():
            project_type = project["project_type"]
            if project_type not in type_stats:
                type_stats[project_type] = 0
            type_stats[project_type] += 1
        
        total_test_cases = sum(p.get("test_cases_count", 0) for p in self.projects_db.values())
        
        return {
            "total_projects": total_projects,
            "active_projects": active_projects,
            "inactive_projects": total_projects - active_projects,
            "type_distribution": type_stats,
            "total_test_cases": total_test_cases,
            "average_test_cases_per_project": total_test_cases / total_projects if total_projects > 0 else 0
        }
    
    def search_projects(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索项目"""
        keyword = keyword.lower()
        results = []
        
        for project in self.projects_db.values():
            if (keyword in project["name"].lower() or 
                keyword in project["description"].lower() or
                any(keyword in feature.lower() for feature in project.get("features", []))):
                results.append(project)
        
        return results
    
    def get_recent_projects(self, limit: int = 5) -> List[Dict[str, Any]]:
        """获取最近的项目"""
        projects = list(self.projects_db.values())
        projects.sort(key=lambda x: x["updated_at"], reverse=True)
        return projects[:limit]
