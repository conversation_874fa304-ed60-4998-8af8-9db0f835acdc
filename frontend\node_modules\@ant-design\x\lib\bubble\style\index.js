"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.prepareComponentToken = exports.default = void 0;
var _cssinjs = require("@ant-design/cssinjs");
var _cssinjsUtils = require("@ant-design/cssinjs-utils");
var _genStyleUtils = require("../../theme/genStyleUtils");
var _content = require("./content");
var _list = _interopRequireDefault(require("./list"));
const loadingMove = new _cssinjs.Keyframes('loadingMove', {
  '0%': {
    transform: 'translateY(0)'
  },
  '10%': {
    transform: 'translateY(4px)'
  },
  '20%': {
    transform: 'translateY(0)'
  },
  '30%': {
    transform: 'translateY(-4px)'
  },
  '40%': {
    transform: 'translateY(0)'
  }
});
const cursorBlink = new _cssinjs.Keyframes('cursorBlink', {
  '0%': {
    opacity: 1
  },
  '50%': {
    opacity: 0
  },
  '100%': {
    opacity: 1
  }
});

// biome-ignore lint/suspicious/noEmptyInterface: ComponentToken need to be empty by default

const genBubbleStyle = token => {
  const {
    componentCls,
    fontSize,
    lineHeight,
    paddingSM,
    colorText,
    calc
  } = token;
  return {
    [componentCls]: {
      display: 'flex',
      columnGap: paddingSM,
      [`&${componentCls}-end`]: {
        justifyContent: 'end',
        flexDirection: 'row-reverse',
        [`& ${componentCls}-content-wrapper`]: {
          alignItems: 'flex-end'
        }
      },
      [`&${componentCls}-rtl`]: {
        direction: 'rtl'
      },
      [`&${componentCls}-typing ${componentCls}-content:last-child::after`]: {
        content: '"|"',
        fontWeight: 900,
        userSelect: 'none',
        opacity: 1,
        marginInlineStart: '0.1em',
        animationName: cursorBlink,
        animationDuration: '0.8s',
        animationIterationCount: 'infinite',
        animationTimingFunction: 'linear'
      },
      // ============================ Avatar =============================
      [`& ${componentCls}-avatar`]: {
        display: 'inline-flex',
        justifyContent: 'center',
        alignSelf: 'flex-start'
      },
      // ======================== Header & Footer ========================
      [`& ${componentCls}-header, & ${componentCls}-footer`]: {
        fontSize: fontSize,
        lineHeight: lineHeight,
        color: token.colorText
      },
      [`& ${componentCls}-header`]: {
        marginBottom: token.paddingXXS
      },
      [`& ${componentCls}-footer`]: {
        marginTop: paddingSM
      },
      // =========================== Content =============================
      [`& ${componentCls}-content-wrapper`]: {
        flex: 'auto',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
        minWidth: 0,
        maxWidth: '100%'
      },
      [`& ${componentCls}-content`]: {
        position: 'relative',
        boxSizing: 'border-box',
        minWidth: 0,
        maxWidth: '100%',
        color: colorText,
        fontSize: token.fontSize,
        lineHeight: token.lineHeight,
        minHeight: calc(paddingSM).mul(2).add(calc(lineHeight).mul(fontSize)).equal(),
        wordBreak: 'break-word',
        [`& ${componentCls}-dot`]: {
          position: 'relative',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          columnGap: token.marginXS,
          padding: `0 ${(0, _cssinjs.unit)(token.paddingXXS)}`,
          '&-item': {
            backgroundColor: token.colorPrimary,
            borderRadius: '100%',
            width: 4,
            height: 4,
            animationName: loadingMove,
            animationDuration: '2s',
            animationIterationCount: 'infinite',
            animationTimingFunction: 'linear',
            '&:nth-child(1)': {
              animationDelay: '0s'
            },
            '&:nth-child(2)': {
              animationDelay: '0.2s'
            },
            '&:nth-child(3)': {
              animationDelay: '0.4s'
            }
          }
        }
      }
    }
  };
};
const prepareComponentToken = () => ({});
exports.prepareComponentToken = prepareComponentToken;
var _default = exports.default = (0, _genStyleUtils.genStyleHooks)('Bubble', token => {
  const bubbleToken = (0, _cssinjsUtils.mergeToken)(token, {});
  return [genBubbleStyle(bubbleToken), (0, _list.default)(bubbleToken), (0, _content.genVariantStyle)(bubbleToken), (0, _content.genShapeStyle)(bubbleToken)];
}, prepareComponentToken);