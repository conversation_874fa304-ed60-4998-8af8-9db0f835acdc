!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("React"),require("ReactDOM"),require("antd"),require("antdCssinjs")):"function"==typeof define&&define.amd?define(["React","ReactDOM","antd","antdCssinjs"],t):"object"==typeof exports?exports.antdx=t(require("React"),require("ReactDOM"),require("antd"),require("antdCssinjs")):e.antdx=t(e.<PERSON>act,e.ReactDOM,e.antd,e.antdCssinjs)}(self,(function(e,t,n,r){return function(){var o={601:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return!("undefined"==typeof window||!window.document||!window.document.createElement)}},800:function(e,t,n){"use strict";var r=n(139).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=o.useRef();t.current=e;var n=o.useCallback((function(){for(var e,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(r))}),[]);return n};var o=r(n(24))},208:function(e,t,n){"use strict";var r=n(458).default,o=n(139).default;Object.defineProperty(t,"__esModule",{value:!0}),t.useLayoutUpdateEffect=t.default=void 0;var a=o(n(24)),i=(0,r(n(601)).default)()?a.useLayoutEffect:a.useEffect,s=function(e,t){var n=a.useRef(!0);i((function(){return e(n.current)}),t),i((function(){return n.current=!1,function(){n.current=!0}}),[])};t.useLayoutUpdateEffect=function(e,t){s((function(t){if(!t)return e()}),t)},t.default=s},905:function(e,t,n){"use strict";var r=n(458).default;t.Z=function(e,t){var n=t||{},r=n.defaultValue,c=n.value,u=n.onChange,d=n.postState,f=(0,s.default)((function(){return l(c)?c:l(r)?"function"==typeof r?r():r:"function"==typeof e?e():e})),m=(0,o.default)(f,2),p=m[0],h=m[1],g=void 0!==c?c:p,v=d?d(g):g,y=(0,a.default)(u),b=(0,s.default)([g]),x=(0,o.default)(b,2),S=x[0],w=x[1];(0,i.useLayoutUpdateEffect)((function(){var e=S[0];p!==e&&y(p,e)}),[S]),(0,i.useLayoutUpdateEffect)((function(){l(c)||h(c)}),[c]);var C=(0,a.default)((function(e,t){h(e,t),w([g],t)}));return[v,C]};var o=r(n(583)),a=r(n(800)),i=n(208),s=r(n(386));function l(e){return void 0!==e}},386:function(e,t,n){"use strict";var r=n(139).default,o=n(458).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=i.useRef(!1),n=i.useState(e),r=(0,a.default)(n,2),o=r[0],s=r[1];return i.useEffect((function(){return t.current=!1,function(){t.current=!0}}),[]),[o,function(e,n){if(n&&t.current)return;s(e)}]};var a=o(n(583)),i=r(n(24))},918:function(e,t,n){"use strict";var r=n(458).default;t.Z=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t=!1===n?{aria:!0,data:!0,attr:!0}:!0===n?{aria:!0}:(0,o.default)({},n);var r={};return Object.keys(e).forEach((function(n){(t.aria&&("role"===n||l(n,i))||t.data&&l(n,s)||t.attr&&a.includes(n))&&(r[n]=e[n])})),r};var o=r(n(783)),a="".concat("accept acceptCharset accessKey action allowFullScreen allowTransparency\n    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge\n    charSet checked classID className colSpan cols content contentEditable contextMenu\n    controls coords crossOrigin data dateTime default defer dir disabled download draggable\n    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder\n    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity\n    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media\n    mediaGroup method min minLength multiple muted name noValidate nonce open\n    optimum pattern placeholder poster preload radioGroup readOnly rel required\n    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected\n    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style\n    summary tabIndex target title type useMap value width wmode wrap"," ").concat("onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown\n    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick\n    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown\n    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel\n    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough\n    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata\n    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError").split(/[\s\n]+/),i="aria-",s="data-";function l(e,t){return 0===e.indexOf(t)}},372:function(e,t){"use strict";t.Z=function(e,t){for(var n=e,r=0;r<t.length;r+=1){if(null==n)return;n=n[t[r]]}return n}},948:function(e,t){"use strict";var n,r=Symbol.for("react.element"),o=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),c=Symbol.for("react.context"),u=Symbol.for("react.server_context"),d=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),m=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case a:case s:case i:case f:case m:return e;default:switch(e=e&&e.$$typeof){case u:case c:case d:case h:case p:case l:return e;default:return t}}case o:return t}}}n=Symbol.for("react.module.reference"),t.ForwardRef=d,t.isMemo=function(e){return v(e)===p}},336:function(e,t,n){"use strict";e.exports=n(948)},24:function(t){"use strict";t.exports=e},314:function(e){"use strict";e.exports=t},721:function(e){"use strict";e.exports=n},781:function(e){"use strict";e.exports=r},14:function(e){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports.default=e.exports},447:function(e){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},753:function(e,t,n){var r=n(819);e.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},177:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},458:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},139:function(e,t,n){var r=n(669).default;function o(t,n){if("function"==typeof WeakMap)var a=new WeakMap,i=new WeakMap;return(e.exports=o=function(e,t){if(!t&&e&&e.__esModule)return e;var n,o,s={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return s;if(n=t?i:a){if(n.has(e))return n.get(e);n.set(e,s)}for(var l in e)"default"!==l&&{}.hasOwnProperty.call(e,l)&&((o=(n=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,l))&&(o.get||o.set)?n(s,l,o):s[l]=e[l]);return s},e.exports.__esModule=!0,e.exports.default=e.exports)(t,n)}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports},228:function(e){e.exports=function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,s=[],l=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(c)throw o}}return s}},e.exports.__esModule=!0,e.exports.default=e.exports},867:function(e){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},783:function(e,t,n){var r=n(753);function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}e.exports=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e},e.exports.__esModule=!0,e.exports.default=e.exports},583:function(e,t,n){var r=n(447),o=n(228),a=n(687),i=n(867);e.exports=function(e,t){return r(e)||o(e,t)||a(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},29:function(e,t,n){var r=n(669).default;e.exports=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},819:function(e,t,n){var r=n(669).default,o=n(29);e.exports=function(e){var t=o(e,"string");return"symbol"==r(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},669:function(e){function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},687:function(e,t,n){var r=n(14);e.exports=function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},35:function(e,t){var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=i(e,a(n)))}return e}function a(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=i(t,n));return t}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},a={};function i(e){var t=a[e];if(void 0!==t)return t.exports;var n=a[e]={exports:{}};return o[e](n,n.exports,i),n.exports}i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,{a:t}),t},i.d=function(e,t){for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};return function(){"use strict";i.r(s),i.d(s,{Actions:function(){return nn},Attachments:function(){return ho},Bubble:function(){return Ea},Conversations:function(){return Oa},Prompts:function(){return Da},Sender:function(){return oa},Suggestion:function(){return ii},ThoughtChain:function(){return ti},Welcome:function(){return di},XProvider:function(){return fi},XRequest:function(){return yi},XStream:function(){return hi},useXAgent:function(){return Si},useXChat:function(){return mi},version:function(){return e}});var e="1.5.0",t=i(177),n=i.n(t),r=i(721),o=i(35),a=i.n(o),l=i(918),c=i(24),u=i.n(c);var d=u().createContext({});const f={classNames:{},styles:{},className:"",style:{}};var m=e=>{const t=u().useContext(d);return u().useMemo((()=>({...f,...t[e]})),[t[e]])};var p=function(){const{getPrefixCls:e,direction:t,csp:n,iconPrefixCls:o,theme:a}=u().useContext(r.ConfigProvider.ConfigContext);return{theme:a,getPrefixCls:e,direction:t,csp:n,iconPrefixCls:o}};function h(){return h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},h.apply(null,arguments)}var g={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"};function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,s=[],l=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(c)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return v(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?v(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function x(e){var t=function(e,t){if("object"!=b(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=b(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==b(t)?t:t+""}function S(e,t,n){return(t=x(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function w(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}const C=Math.round;function E(e,t){const n=e.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],r=n.map((e=>parseFloat(e)));for(let e=0;e<3;e+=1)r[e]=t(r[e]||0,n[e]||"",e);return n[3]?r[3]=n[3].includes("%")?r[3]/100:r[3]:r[3]=1,r}const $=(e,t,n)=>0===n?e:e/100;function k(e,t){const n=t||255;return e>n?n:e<0?0:e}class M{constructor(e){function t(t){return t[0]in e&&t[1]in e&&t[2]in e}if(S(this,"isValid",!0),S(this,"r",0),S(this,"g",0),S(this,"b",0),S(this,"a",1),S(this,"_h",void 0),S(this,"_s",void 0),S(this,"_l",void 0),S(this,"_v",void 0),S(this,"_max",void 0),S(this,"_min",void 0),S(this,"_brightness",void 0),e)if("string"==typeof e){const n=e.trim();function r(e){return n.startsWith(e)}/^#?[A-F\d]{3,8}$/i.test(n)?this.fromHexString(n):r("rgb")?this.fromRgbString(n):r("hsl")?this.fromHslString(n):(r("hsv")||r("hsb"))&&this.fromHsvString(n)}else if(e instanceof M)this.r=e.r,this.g=e.g,this.b=e.b,this.a=e.a,this._h=e._h,this._s=e._s,this._l=e._l,this._v=e._v;else if(t("rgb"))this.r=k(e.r),this.g=k(e.g),this.b=k(e.b),this.a="number"==typeof e.a?k(e.a,1):1;else if(t("hsl"))this.fromHsl(e);else{if(!t("hsv"))throw new Error("@ant-design/fast-color: unsupported input "+JSON.stringify(e));this.fromHsv(e)}else;}setR(e){return this._sc("r",e)}setG(e){return this._sc("g",e)}setB(e){return this._sc("b",e)}setA(e){return this._sc("a",e,1)}setHue(e){const t=this.toHsv();return t.h=e,this._c(t)}getLuminance(){function e(e){const t=e/255;return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}return.2126*e(this.r)+.7152*e(this.g)+.0722*e(this.b)}getHue(){if(void 0===this._h){const e=this.getMax()-this.getMin();this._h=0===e?0:C(60*(this.r===this.getMax()?(this.g-this.b)/e+(this.g<this.b?6:0):this.g===this.getMax()?(this.b-this.r)/e+2:(this.r-this.g)/e+4))}return this._h}getSaturation(){if(void 0===this._s){const e=this.getMax()-this.getMin();this._s=0===e?0:e/this.getMax()}return this._s}getLightness(){return void 0===this._l&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return void 0===this._v&&(this._v=this.getMax()/255),this._v}getBrightness(){return void 0===this._brightness&&(this._brightness=(299*this.r+587*this.g+114*this.b)/1e3),this._brightness}darken(e=10){const t=this.getHue(),n=this.getSaturation();let r=this.getLightness()-e/100;return r<0&&(r=0),this._c({h:t,s:n,l:r,a:this.a})}lighten(e=10){const t=this.getHue(),n=this.getSaturation();let r=this.getLightness()+e/100;return r>1&&(r=1),this._c({h:t,s:n,l:r,a:this.a})}mix(e,t=50){const n=this._c(e),r=t/100,o=e=>(n[e]-this[e])*r+this[e],a={r:C(o("r")),g:C(o("g")),b:C(o("b")),a:C(100*o("a"))/100};return this._c(a)}tint(e=10){return this.mix({r:255,g:255,b:255,a:1},e)}shade(e=10){return this.mix({r:0,g:0,b:0,a:1},e)}onBackground(e){const t=this._c(e),n=this.a+t.a*(1-this.a),r=e=>C((this[e]*this.a+t[e]*t.a*(1-this.a))/n);return this._c({r:r("r"),g:r("g"),b:r("b"),a:n})}isDark(){return this.getBrightness()<128}isLight(){return this.getBrightness()>=128}equals(e){return this.r===e.r&&this.g===e.g&&this.b===e.b&&this.a===e.a}clone(){return this._c(this)}toHexString(){let e="#";const t=(this.r||0).toString(16);e+=2===t.length?t:"0"+t;const n=(this.g||0).toString(16);e+=2===n.length?n:"0"+n;const r=(this.b||0).toString(16);if(e+=2===r.length?r:"0"+r,"number"==typeof this.a&&this.a>=0&&this.a<1){const t=C(255*this.a).toString(16);e+=2===t.length?t:"0"+t}return e}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){const e=this.getHue(),t=C(100*this.getSaturation()),n=C(100*this.getLightness());return 1!==this.a?`hsla(${e},${t}%,${n}%,${this.a})`:`hsl(${e},${t}%,${n}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return 1!==this.a?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(e,t,n){const r=this.clone();return r[e]=k(t,n),r}_c(e){return new this.constructor(e)}getMax(){return void 0===this._max&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return void 0===this._min&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(e){const t=e.replace("#","");function n(e,n){return parseInt(t[e]+t[n||e],16)}t.length<6?(this.r=n(0),this.g=n(1),this.b=n(2),this.a=t[3]?n(3)/255:1):(this.r=n(0,1),this.g=n(2,3),this.b=n(4,5),this.a=t[6]?n(6,7)/255:1)}fromHsl({h:e,s:t,l:n,a:r}){if(this._h=e%360,this._s=t,this._l=n,this.a="number"==typeof r?r:1,t<=0){const e=C(255*n);this.r=e,this.g=e,this.b=e}let o=0,a=0,i=0;const s=e/60,l=(1-Math.abs(2*n-1))*t,c=l*(1-Math.abs(s%2-1));s>=0&&s<1?(o=l,a=c):s>=1&&s<2?(o=c,a=l):s>=2&&s<3?(a=l,i=c):s>=3&&s<4?(a=c,i=l):s>=4&&s<5?(o=c,i=l):s>=5&&s<6&&(o=l,i=c);const u=n-l/2;this.r=C(255*(o+u)),this.g=C(255*(a+u)),this.b=C(255*(i+u))}fromHsv({h:e,s:t,v:n,a:r}){this._h=e%360,this._s=t,this._v=n,this.a="number"==typeof r?r:1;const o=C(255*n);if(this.r=o,this.g=o,this.b=o,t<=0)return;const a=e/60,i=Math.floor(a),s=a-i,l=C(n*(1-t)*255),c=C(n*(1-t*s)*255),u=C(n*(1-t*(1-s))*255);switch(i){case 0:this.g=u,this.b=l;break;case 1:this.r=c,this.b=l;break;case 2:this.r=l,this.b=u;break;case 3:this.r=l,this.g=c;break;case 4:this.r=u,this.g=l;break;default:this.g=l,this.b=c}}fromHsvString(e){const t=E(e,$);this.fromHsv({h:t[0],s:t[1],v:t[2],a:t[3]})}fromHslString(e){const t=E(e,$);this.fromHsl({h:t[0],s:t[1],l:t[2],a:t[3]})}fromRgbString(e){const t=E(e,((e,t)=>t.includes("%")?C(e/100*255):e));this.r=t[0],this.g=t[1],this.b=t[2],this.a=t[3]}}var R=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function N(e,t,n){var r;return(r=Math.round(e.h)>=60&&Math.round(e.h)<=240?n?Math.round(e.h)-2*t:Math.round(e.h)+2*t:n?Math.round(e.h)+2*t:Math.round(e.h)-2*t)<0?r+=360:r>=360&&(r-=360),r}function L(e,t,n){return 0===e.h&&0===e.s?e.s:((r=n?e.s-.16*t:4===t?e.s+.16:e.s+.05*t)>1&&(r=1),n&&5===t&&r>.1&&(r=.1),r<.06&&(r=.06),Math.round(100*r)/100);var r}function z(e,t,n){var r;return r=n?e.v+.05*t:e.v-.15*t,r=Math.max(0,Math.min(1,r)),Math.round(100*r)/100}var T=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];T.primary=T[5];var O=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];O.primary=O[5];var H=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];H.primary=H[5];var P=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];P.primary=P[5];var j=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];j.primary=j[5];var _=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];_.primary=_[5];var D=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];D.primary=D[5];var I=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];I.primary=I[5];var A=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];A.primary=A[5];var B=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];B.primary=B[5];var X=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];X.primary=X[5];var F=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];F.primary=F[5];var q=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];q.primary=q[5];var V=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];V.primary=V[5];var W=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];W.primary=W[5];var G=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];G.primary=G[5];var U=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];U.primary=U[5];var K=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];K.primary=K[5];var Z=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];Z.primary=Z[5];var Y=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];Y.primary=Y[5];var Q=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];Q.primary=Q[5];var J=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];J.primary=J[5];var ee=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];ee.primary=ee[5];var te=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];te.primary=te[5];var ne=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];ne.primary=ne[5];var re=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];re.primary=re[5];var oe=(0,c.createContext)({});function ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ie(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ae(Object(n),!0).forEach((function(t){S(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ae(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function se(){return!("undefined"==typeof window||!window.document||!window.document.createElement)}var le="data-rc-order",ce="data-rc-priority",ue="rc-util-key",de=new Map;function fe(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.mark;return t?t.startsWith("data-")?t:"data-".concat(t):ue}function me(e){return e.attachTo?e.attachTo:document.querySelector("head")||document.body}function pe(e){return"queue"===e?"prependQueue":e?"prepend":"append"}function he(e){return Array.from((de.get(e)||e).children).filter((function(e){return"STYLE"===e.tagName}))}function ge(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!se())return null;var n=t.csp,r=t.prepend,o=t.priority,a=void 0===o?0:o,i=pe(r),s="prependQueue"===i,l=document.createElement("style");l.setAttribute(le,i),s&&a&&l.setAttribute(ce,"".concat(a)),null!=n&&n.nonce&&(l.nonce=null==n?void 0:n.nonce),l.innerHTML=e;var c=me(t),u=c.firstChild;if(r){if(s){var d=(t.styles||he(c)).filter((function(e){if(!["prepend","prependQueue"].includes(e.getAttribute(le)))return!1;var t=Number(e.getAttribute(ce)||0);return a>=t}));if(d.length)return c.insertBefore(l,d[d.length-1].nextSibling),l}c.insertBefore(l,u)}else c.appendChild(l);return l}function ve(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=me(t);return(t.styles||he(n)).find((function(n){return n.getAttribute(fe(t))===e}))}function ye(e,t){var n=de.get(e);if(!n||!function(e,t){if(!e)return!1;if(e.contains)return e.contains(t);for(var n=t;n;){if(n===e)return!0;n=n.parentNode}return!1}(document,n)){var r=ge("",t),o=r.parentNode;de.set(e,o),e.removeChild(r)}}function be(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}function xe(e){return function(e){return be(e)instanceof ShadowRoot}(e)?be(e):null}var Se={},we=[];function Ce(e,t){}function Ee(e,t){}function $e(e,t,n){t||Se[n]||(e(!1,n),Se[n]=!0)}function ke(e,t){$e(Ce,e,t)}ke.preMessage=function(e){we.push(e)},ke.resetWarned=function(){Se={}},ke.noteOnce=function(e,t){$e(Ee,e,t)};var Me=ke;function Re(e){return e.replace(/-(.)/g,(function(e,t){return t.toUpperCase()}))}function Ne(e){return"object"===b(e)&&"string"==typeof e.name&&"string"==typeof e.theme&&("object"===b(e.icon)||"function"==typeof e.icon)}function Le(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce((function(t,n){var r=e[n];if("class"===n)t.className=r,delete t.class;else delete t[n],t[Re(n)]=r;return t}),{})}function ze(e,t,n){return n?u().createElement(e.tag,ie(ie({key:t},Le(e.attrs)),n),(e.children||[]).map((function(n,r){return ze(n,"".concat(t,"-").concat(e.tag,"-").concat(r))}))):u().createElement(e.tag,ie({key:t},Le(e.attrs)),(e.children||[]).map((function(n,r){return ze(n,"".concat(t,"-").concat(e.tag,"-").concat(r))})))}function Te(e){return function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[],r=new M(e),o=r.toHsv(),a=5;a>0;a-=1){var i=new M({h:N(o,a,!0),s:L(o,a,!0),v:z(o,a,!0)});n.push(i)}n.push(r);for(var s=1;s<=4;s+=1){var l=new M({h:N(o,s),s:L(o,s),v:z(o,s)});n.push(l)}return"dark"===t.theme?R.map((function(e){var r=e.index,o=e.amount;return new M(t.backgroundColor||"#141414").mix(n[r],o).toHexString()})):n.map((function(e){return e.toHexString()}))}(e)[0]}function Oe(e){return e?Array.isArray(e)?e:[e]:[]}var He=function(e){var t=(0,c.useContext)(oe),n=t.csp,r=t.prefixCls,o=t.layer,a="\n.anticon {\n  display: inline-flex;\n  align-items: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n";r&&(a=a.replace(/anticon/g,r)),o&&(a="@layer ".concat(o," {\n").concat(a,"\n}")),(0,c.useEffect)((function(){var t=xe(e.current);!function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=me(n),o=he(r),a=ie(ie({},n),{},{styles:o});ye(r,a);var i=ve(t,a);if(i){var s,l,c;return null!==(s=a.csp)&&void 0!==s&&s.nonce&&i.nonce!==(null===(l=a.csp)||void 0===l?void 0:l.nonce)&&(i.nonce=null===(c=a.csp)||void 0===c?void 0:c.nonce),i.innerHTML!==e&&(i.innerHTML=e),i}var u=ge(e,a);u.setAttribute(fe(a),t)}(a,"@ant-design-icons",{prepend:!o,csp:n,attachTo:t})}),[])},Pe=["icon","className","onClick","style","primaryColor","secondaryColor"],je={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};var _e=function(e){var t,n,r=e.icon,o=e.className,a=e.onClick,i=e.style,s=e.primaryColor,l=e.secondaryColor,u=w(e,Pe),d=c.useRef(),f=je;if(s&&(f={primaryColor:s,secondaryColor:l||Te(s)}),He(d),t=Ne(r),n="icon should be icon definiton, but got ".concat(r),Me(t,"[@ant-design/icons] ".concat(n)),!Ne(r))return null;var m=r;return m&&"function"==typeof m.icon&&(m=ie(ie({},m),{},{icon:m.icon(f.primaryColor,f.secondaryColor)})),ze(m.icon,"svg-".concat(m.name),ie(ie({className:o,onClick:a,style:i,"data-icon":m.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},u),{},{ref:d}))};_e.displayName="IconReact",_e.getTwoToneColors=function(){return ie({},je)},_e.setTwoToneColors=function(e){var t=e.primaryColor,n=e.secondaryColor;je.primaryColor=t,je.secondaryColor=n||Te(t),je.calculated=!!n};var De=_e;function Ie(e){var t=y(Oe(e),2),n=t[0],r=t[1];return De.setTwoToneColors({primaryColor:n,secondaryColor:r})}var Ae=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];Ie(A.primary);var Be=c.forwardRef((function(e,t){var n=e.className,r=e.icon,o=e.spin,i=e.rotate,s=e.tabIndex,l=e.onClick,u=e.twoToneColor,d=w(e,Ae),f=c.useContext(oe),m=f.prefixCls,p=void 0===m?"anticon":m,g=f.rootClassName,v=a()(g,p,S(S({},"".concat(p,"-").concat(r.name),!!r.name),"".concat(p,"-spin"),!!o||"loading"===r.name),n),b=s;void 0===b&&l&&(b=-1);var x=i?{msTransform:"rotate(".concat(i,"deg)"),transform:"rotate(".concat(i,"deg)")}:void 0,C=y(Oe(u),2),E=C[0],$=C[1];return c.createElement("span",h({role:"img","aria-label":r.name},d,{ref:t,tabIndex:b,onClick:l,className:v}),c.createElement(De,{icon:r,primaryColor:E,secondaryColor:$,style:x}))}));Be.displayName="AntdIcon",Be.getTwoToneColor=function(){var e=De.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor},Be.setTwoToneColor=Ie;var Xe=Be,Fe=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:g}))};var qe=c.forwardRef(Fe);const Ve=(e,t)=>{const n=e[0];for(const r of t)if(r.key===n){if(1===e.length)return r;if("children"in r)return Ve(e.slice(1),r.children)}return null};var We=e=>{const{onClick:t,item:n}=e,{children:o=[],triggerSubMenuAction:a="hover"}=n,{getPrefixCls:i}=p(),s=i("actions",e.prefixCls),l=n?.icon??u().createElement(qe,null),c={items:o,onClick:({key:e,keyPath:r,domEvent:a})=>{Ve(r,o)?.onItemClick?Ve(r,o)?.onItemClick?.(Ve(r,o)):t?.({key:e,keyPath:[...r,n.key],domEvent:a,item:Ve(r,o)})}};return u().createElement(r.Dropdown,{menu:c,overlayClassName:`${s}-sub-item`,arrow:!0,trigger:[a]},u().createElement("div",{className:`${s}-list-item`},u().createElement("div",{className:`${s}-list-item-icon`},l)))},Ge=i(781);function Ue(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ke(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,x(r.key),r)}}function Ze(e,t,n){return t&&Ke(e.prototype,t),n&&Ke(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ye(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Qe(e,t){return Qe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Qe(e,t)}function Je(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qe(e,t)}function et(e){return et=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},et(e)}function tt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(tt=function(){return!!e})()}function nt(e,t){if(t&&("object"==b(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ye(e)}function rt(e){var t=tt();return function(){var n,r=et(e);if(t){var o=et(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return nt(this,n)}}var ot=Ze((function e(){Ue(this,e)})),at="CALC_UNIT",it=new RegExp(at,"g");function st(e){return"number"==typeof e?"".concat(e).concat(at):e}var lt=function(e){Je(n,e);var t=rt(n);function n(e,r){var o;Ue(this,n),S(Ye(o=t.call(this)),"result",""),S(Ye(o),"unitlessCssVar",void 0),S(Ye(o),"lowPriority",void 0);var a=b(e);return o.unitlessCssVar=r,e instanceof n?o.result="(".concat(e.result,")"):"number"===a?o.result=st(e):"string"===a&&(o.result=e),o}return Ze(n,[{key:"add",value:function(e){return e instanceof n?this.result="".concat(this.result," + ").concat(e.getResult()):"number"!=typeof e&&"string"!=typeof e||(this.result="".concat(this.result," + ").concat(st(e))),this.lowPriority=!0,this}},{key:"sub",value:function(e){return e instanceof n?this.result="".concat(this.result," - ").concat(e.getResult()):"number"!=typeof e&&"string"!=typeof e||(this.result="".concat(this.result," - ").concat(st(e))),this.lowPriority=!0,this}},{key:"mul",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof n?this.result="".concat(this.result," * ").concat(e.getResult(!0)):"number"!=typeof e&&"string"!=typeof e||(this.result="".concat(this.result," * ").concat(e)),this.lowPriority=!1,this}},{key:"div",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof n?this.result="".concat(this.result," / ").concat(e.getResult(!0)):"number"!=typeof e&&"string"!=typeof e||(this.result="".concat(this.result," / ").concat(e)),this.lowPriority=!1,this}},{key:"getResult",value:function(e){return this.lowPriority||e?"(".concat(this.result,")"):this.result}},{key:"equal",value:function(e){var t=this,n=(e||{}).unit,r=!0;return"boolean"==typeof n?r=n:Array.from(this.unitlessCssVar).some((function(e){return t.result.includes(e)}))&&(r=!1),this.result=this.result.replace(it,r?"px":""),void 0!==this.lowPriority?"calc(".concat(this.result,")"):this.result}}]),n}(ot),ct=function(e){Je(n,e);var t=rt(n);function n(e){var r;return Ue(this,n),S(Ye(r=t.call(this)),"result",0),e instanceof n?r.result=e.result:"number"==typeof e&&(r.result=e),r}return Ze(n,[{key:"add",value:function(e){return e instanceof n?this.result+=e.result:"number"==typeof e&&(this.result+=e),this}},{key:"sub",value:function(e){return e instanceof n?this.result-=e.result:"number"==typeof e&&(this.result-=e),this}},{key:"mul",value:function(e){return e instanceof n?this.result*=e.result:"number"==typeof e&&(this.result*=e),this}},{key:"div",value:function(e){return e instanceof n?this.result/=e.result:"number"==typeof e&&(this.result/=e),this}},{key:"equal",value:function(){return this.result}}]),n}(ot),ut=function(e,t){var n="css"===e?lt:ct;return function(e){return new n(e,t)}},dt=function(e,t){return"".concat([t,e.replace(/([A-Z]+)([A-Z][a-z]+)/g,"$1-$2").replace(/([a-z])([A-Z])/g,"$1-$2")].filter(Boolean).join("-"))};function ft(e){var t=c.useRef();t.current=e;var n=c.useCallback((function(){for(var e,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(r))}),[]);return n}var mt=se()?c.useLayoutEffect:c.useEffect,pt=function(e,t){var n=c.useRef(!0);mt((function(){return e(n.current)}),t),mt((function(){return n.current=!1,function(){n.current=!0}}),[])},ht=function(e,t){pt((function(t){if(!t)return e()}),t)};function gt(e){var t=c.useRef(!1),n=y(c.useState(e),2),r=n[0],o=n[1];return c.useEffect((function(){return t.current=!1,function(){t.current=!0}}),[]),[r,function(e,n){n&&t.current||o(e)}]}function vt(e){return void 0!==e}function yt(e,t){var n=t||{},r=n.defaultValue,o=n.value,a=n.onChange,i=n.postState,s=y(gt((function(){return vt(o)?o:vt(r)?"function"==typeof r?r():r:"function"==typeof e?e():e})),2),l=s[0],c=s[1],u=void 0!==o?o:l,d=i?i(u):u,f=ft(a),m=y(gt([u]),2),p=m[0],h=m[1];return ht((function(){var e=p[0];l!==e&&f(l,e)}),[p]),ht((function(){vt(o)||c(o)}),[o]),[d,ft((function(e,t){c(e,t),h([u],t)}))]}var bt=i(336);var xt=Symbol.for("react.element"),St=Symbol.for("react.transitional.element"),wt=Symbol.for("react.fragment");var Ct=Number(c.version.split(".")[0]),Et=function(e,t){"function"==typeof e?e(t):"object"===b(e)&&e&&"current"in e&&(e.current=t)},$t=function(e){var t,n;if(!e)return!1;if(kt(e)&&Ct>=19)return!0;var r=(0,bt.isMemo)(e)?e.type.type:e.type;return!!("function"!=typeof r||null!==(t=r.prototype)&&void 0!==t&&t.render||r.$$typeof===bt.ForwardRef)&&!!("function"!=typeof e||null!==(n=e.prototype)&&void 0!==n&&n.render||e.$$typeof===bt.ForwardRef)};function kt(e){return(0,c.isValidElement)(e)&&!((t=e)&&"object"===b(t)&&(t.$$typeof===xt||t.$$typeof===St)&&t.type===wt);var t}"undefined"==typeof Reflect?Object.keys:Reflect.ownKeys;var Mt=function(e,t,n,r){var o=ie({},t[e]);null!=r&&r.deprecatedTokens&&r.deprecatedTokens.forEach((function(e){var t,n=y(e,2),r=n[0],a=n[1];(null!=o&&o[r]||null!=o&&o[a])&&(null!==(t=o[a])&&void 0!==t||(o[a]=null==o?void 0:o[r]))}));var a=ie(ie({},n),o);return Object.keys(a).forEach((function(e){a[e]===t[e]&&delete a[e]})),a},Rt="undefined"!=typeof CSSINJS_STATISTIC,Nt=!0;function Lt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(!Rt)return Object.assign.apply(Object,[{}].concat(t));Nt=!1;var r={};return t.forEach((function(e){"object"===b(e)&&Object.keys(e).forEach((function(t){Object.defineProperty(r,t,{configurable:!0,enumerable:!0,get:function(){return e[t]}})}))})),Nt=!0,r}var zt={};function Tt(){}var Ot=function(e){var t,n=e,r=Tt;return Rt&&"undefined"!=typeof Proxy&&(t=new Set,n=new Proxy(e,{get:function(e,n){var r;Nt&&(null===(r=t)||void 0===r||r.add(n));return e[n]}}),r=function(e,n){var r;zt[e]={global:Array.from(t),component:ie(ie({},null===(r=zt[e])||void 0===r?void 0:r.component),n)}}),{token:n,keys:t,flush:r}};var Ht=function(e,t,n){var r;return"function"==typeof n?n(Lt(t,null!==(r=t[e])&&void 0!==r?r:{})):null!=n?n:{}};var Pt=function(e){return"js"===e?{max:Math.max,min:Math.min}:{max:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return"max(".concat(t.map((function(e){return(0,Ge.unit)(e)})).join(","),")")},min:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return"min(".concat(t.map((function(e){return(0,Ge.unit)(e)})).join(","),")")}}},jt=function(){function e(){Ue(this,e),S(this,"map",new Map),S(this,"objectIDMap",new WeakMap),S(this,"nextID",0),S(this,"lastAccessBeat",new Map),S(this,"accessBeat",0)}return Ze(e,[{key:"set",value:function(e,t){this.clear();var n=this.getCompositeKey(e);this.map.set(n,t),this.lastAccessBeat.set(n,Date.now())}},{key:"get",value:function(e){var t=this.getCompositeKey(e),n=this.map.get(t);return this.lastAccessBeat.set(t,Date.now()),this.accessBeat+=1,n}},{key:"getCompositeKey",value:function(e){var t=this;return e.map((function(e){return e&&"object"===b(e)?"obj_".concat(t.getObjectID(e)):"".concat(b(e),"_").concat(e)})).join("|")}},{key:"getObjectID",value:function(e){if(this.objectIDMap.has(e))return this.objectIDMap.get(e);var t=this.nextID;return this.objectIDMap.set(e,t),this.nextID+=1,t}},{key:"clear",value:function(){var e=this;if(this.accessBeat>1e4){var t=Date.now();this.lastAccessBeat.forEach((function(n,r){t-n>6e5&&(e.map.delete(r),e.lastAccessBeat.delete(r))})),this.accessBeat=0}}}]),e}(),_t=new jt;var Dt=function(e,t){return u().useMemo((function(){var n=_t.get(t);if(n)return n;var r=e();return _t.set(t,r),r}),t)},It=function(){return{}};var At=function(e){var t=e.useCSP,n=void 0===t?It:t,r=e.useToken,o=e.usePrefix,a=e.getResetStyles,i=e.getCommonStyle,s=e.getCompUnitless;function l(t,s,l){var c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=Array.isArray(t)?t:[t,t],d=y(u,1),f=d[0],m=u.join("-"),p=e.layer||{name:"antd"};return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,u=r(),d=u.theme,h=u.realToken,g=u.hashId,v=u.token,y=u.cssVar,x=o(),S=x.rootPrefixCls,w=x.iconPrefixCls,C=n(),E=y?"css":"js",$=Dt((function(){var e=new Set;return y&&Object.keys(c.unitless||{}).forEach((function(t){e.add((0,Ge.token2CSSVar)(t,y.prefix)),e.add((0,Ge.token2CSSVar)(t,dt(f,y.prefix)))})),ut(E,e)}),[E,f,null==y?void 0:y.prefix]),k=Pt(E),M=k.max,R=k.min,N={theme:d,token:v,hashId:g,nonce:function(){return C.nonce},clientOnly:c.clientOnly,layer:p,order:c.order||-999};"function"==typeof a&&(0,Ge.useStyleRegister)(ie(ie({},N),{},{clientOnly:!1,path:["Shared",S]}),(function(){return a(v,{prefix:{rootPrefixCls:S,iconPrefixCls:w},csp:C})}));var L=(0,Ge.useStyleRegister)(ie(ie({},N),{},{path:[m,e,w]}),(function(){if(!1===c.injectStyle)return[];var n=Ot(v),r=n.token,o=n.flush,a=Ht(f,h,l),u=".".concat(e),d=Mt(f,h,a,{deprecatedTokens:c.deprecatedTokens});y&&a&&"object"===b(a)&&Object.keys(a).forEach((function(e){a[e]="var(".concat((0,Ge.token2CSSVar)(e,dt(f,y.prefix)),")")}));var m=Lt(r,{componentCls:u,prefixCls:e,iconCls:".".concat(w),antCls:".".concat(S),calc:$,max:M,min:R},y?a:d),p=s(m,{hashId:g,prefixCls:e,rootPrefixCls:S,iconPrefixCls:w});o(f,d);var x="function"==typeof i?i(m,e,t,c.resetFont):null;return[!1===c.resetStyle?null:x,p]}));return[L,g]}}return{genStyleHooks:function(e,t,n,o){var a=Array.isArray(e)?e[0]:e;function i(e){return"".concat(String(a)).concat(e.slice(0,1).toUpperCase()).concat(e.slice(1))}var c=(null==o?void 0:o.unitless)||{},d=ie(ie({},"function"==typeof s?s(e):{}),{},S({},i("zIndexPopup"),!0));Object.keys(c).forEach((function(e){d[i(e)]=c[e]}));var f=ie(ie({},o),{},{unitless:d,prefixToken:i}),m=l(e,t,n,f),p=function(e,t,n){var o=n.unitless,a=n.injectStyle,i=void 0===a||a,s=n.prefixToken,l=n.ignore,c=function(a){var i=a.rootCls,c=a.cssVar,u=void 0===c?{}:c,d=r().realToken;return(0,Ge.useCSSVarRegister)({path:[e],prefix:u.prefix,key:u.key,unitless:o,ignore:l,token:d,scope:i},(function(){var r=Ht(e,d,t),o=Mt(e,d,r,{deprecatedTokens:null==n?void 0:n.deprecatedTokens});return Object.keys(r).forEach((function(e){o[s(e)]=o[e],delete o[e]})),o})),null},d=function(t){var n=r().cssVar;return[function(r){return i&&n?u().createElement(u().Fragment,null,u().createElement(c,{rootCls:t,cssVar:n,component:e}),r):r},null==n?void 0:n.key]};return d}(a,n,f);return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=m(e,t),r=y(n,2),o=r[1],a=p(t),i=y(a,2),s=i[0],l=i[1];return[s,o,l]}},genSubStyleComponent:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=l(e,t,n,ie({resetStyle:!1,order:-998},r)),a=function(e){var t=e.prefixCls,n=e.rootCls;return o(t,void 0===n?t:n),null};return a},genComponentStyleHook:l}};const Bt={lineHeight:!0,lineHeightSM:!0,lineHeightLG:!0,lineHeightHeading1:!0,lineHeightHeading2:!0,lineHeightHeading3:!0,lineHeightHeading4:!0,lineHeightHeading5:!0,opacityLoading:!0,fontWeightStrong:!0,zIndexPopupBase:!0,zIndexBase:!0,opacityImage:!0},Xt={size:!0,sizeSM:!0,sizeLG:!0,sizeMD:!0,sizeXS:!0,sizeXXS:!0,sizeMS:!0,sizeXL:!0,sizeXXL:!0,sizeUnit:!0,sizeStep:!0,motionBase:!0,motionUnit:!0};var Ft=Object.assign(Object.assign({},{blue:"#1677FF",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#EB2F96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911"}),{colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff4d4f",colorInfo:"#1677ff",colorLink:"",colorTextBase:"",colorBgBase:"",fontFamily:"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,\n'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\n'Noto Color Emoji'",fontFamilyCode:"'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInBack:"cubic-bezier(0.71, -0.46, 0.88, 0.6)",motionEaseInQuint:"cubic-bezier(0.755, 0.05, 0.855, 0.06)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:6,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,motion:!0});function qt(e){return e>=0&&e<=255}var Vt=function(e,t){const{r:n,g:r,b:o,a:a}=new M(e).toRgb();if(a<1)return e;const{r:i,g:s,b:l}=new M(t).toRgb();for(let e=.01;e<=1;e+=.01){const t=Math.round((n-i*(1-e))/e),a=Math.round((r-s*(1-e))/e),c=Math.round((o-l*(1-e))/e);if(qt(t)&&qt(a)&&qt(c))return new M({r:t,g:a,b:c,a:Math.round(100*e)/100}).toRgbString()}return new M({r:n,g:r,b:o,a:1}).toRgbString()},Wt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const Gt=(0,Ge.createTheme)(r.theme.defaultAlgorithm),Ut={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},Kt=(e,t,n)=>{const r=n.getDerivativeToken(e),{override:o,...a}=t;let i={...r,override:o};return i=function(e){const{override:t}=e,n=Wt(e,["override"]),r=Object.assign({},t);Object.keys(Ft).forEach((e=>{delete r[e]}));const o=Object.assign(Object.assign({},n),r),a=1200,i=1600;if(!1===o.motion){const e="0s";o.motionDurationFast=e,o.motionDurationMid=e,o.motionDurationSlow=e}return Object.assign(Object.assign(Object.assign({},o),{colorFillContent:o.colorFillSecondary,colorFillContentHover:o.colorFill,colorFillAlter:o.colorFillQuaternary,colorBgContainerDisabled:o.colorFillTertiary,colorBorderBg:o.colorBgContainer,colorSplit:Vt(o.colorBorderSecondary,o.colorBgContainer),colorTextPlaceholder:o.colorTextQuaternary,colorTextDisabled:o.colorTextQuaternary,colorTextHeading:o.colorText,colorTextLabel:o.colorTextSecondary,colorTextDescription:o.colorTextTertiary,colorTextLightSolid:o.colorWhite,colorHighlight:o.colorError,colorBgTextHover:o.colorFillSecondary,colorBgTextActive:o.colorFill,colorIcon:o.colorTextTertiary,colorIconHover:o.colorText,colorErrorOutline:Vt(o.colorErrorBg,o.colorBgContainer),colorWarningOutline:Vt(o.colorWarningBg,o.colorBgContainer),fontSizeIcon:o.fontSizeSM,lineWidthFocus:3*o.lineWidth,lineWidth:o.lineWidth,controlOutlineWidth:2*o.lineWidth,controlInteractiveSize:o.controlHeight/2,controlItemBgHover:o.colorFillTertiary,controlItemBgActive:o.colorPrimaryBg,controlItemBgActiveHover:o.colorPrimaryBgHover,controlItemBgActiveDisabled:o.colorFill,controlTmpOutline:o.colorFillQuaternary,controlOutline:Vt(o.colorPrimaryBg,o.colorBgContainer),lineType:o.lineType,borderRadius:o.borderRadius,borderRadiusXS:o.borderRadiusXS,borderRadiusSM:o.borderRadiusSM,borderRadiusLG:o.borderRadiusLG,fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:o.sizeXXS,paddingXS:o.sizeXS,paddingSM:o.sizeSM,padding:o.size,paddingMD:o.sizeMD,paddingLG:o.sizeLG,paddingXL:o.sizeXL,paddingContentHorizontalLG:o.sizeLG,paddingContentVerticalLG:o.sizeMS,paddingContentHorizontal:o.sizeMS,paddingContentVertical:o.sizeSM,paddingContentHorizontalSM:o.size,paddingContentVerticalSM:o.sizeXS,marginXXS:o.sizeXXS,marginXS:o.sizeXS,marginSM:o.sizeSM,margin:o.size,marginMD:o.sizeMD,marginLG:o.sizeLG,marginXL:o.sizeXL,marginXXL:o.sizeXXL,boxShadow:"\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowSecondary:"\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowTertiary:"\n      0 1px 2px 0 rgba(0, 0, 0, 0.03),\n      0 1px 6px -1px rgba(0, 0, 0, 0.02),\n      0 2px 4px 0 rgba(0, 0, 0, 0.02)\n    ",screenXS:480,screenXSMin:480,screenXSMax:575,screenSM:576,screenSMMin:576,screenSMMax:767,screenMD:768,screenMDMin:768,screenMDMax:991,screenLG:992,screenLGMin:992,screenLGMax:1199,screenXL:a,screenXLMin:a,screenXLMax:1599,screenXXL:i,screenXXLMin:i,boxShadowPopoverArrow:"2px 2px 5px rgba(0, 0, 0, 0.05)",boxShadowCard:`\n      0 1px 2px -2px ${new M("rgba(0, 0, 0, 0.16)").toRgbString()},\n      0 3px 6px 0 ${new M("rgba(0, 0, 0, 0.12)").toRgbString()},\n      0 5px 12px 4px ${new M("rgba(0, 0, 0, 0.09)").toRgbString()}\n    `,boxShadowDrawerRight:"\n      -6px 0 16px 0 rgba(0, 0, 0, 0.08),\n      -3px 0 6px -4px rgba(0, 0, 0, 0.12),\n      -9px 0 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowDrawerLeft:"\n      6px 0 16px 0 rgba(0, 0, 0, 0.08),\n      3px 0 6px -4px rgba(0, 0, 0, 0.12),\n      9px 0 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowDrawerUp:"\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowDrawerDown:"\n      0 -6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 -3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 -9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)"}),r)}(i),a&&Object.entries(a).forEach((([e,t])=>{const{theme:n,...r}=t;let o=r;n&&(o=Kt({...i,...r},{override:r},n)),i[e]=o})),i};function Zt(){const{token:t,hashed:n,theme:o=Gt,override:a,cssVar:i}=u().useContext(r.theme._internalContext),[s,l,c]=(0,Ge.useCacheToken)(o,[r.theme.defaultSeed,t],{salt:`${e}-${n||""}`,override:a,getComputedToken:Kt,cssVar:i&&{prefix:i.prefix,key:i.key,unitless:Bt,ignore:Xt,preserve:Ut}});return[o,c,n?l:"",s,i]}const{genStyleHooks:Yt,genComponentStyleHook:Qt,genSubStyleComponent:Jt}=At({usePrefix:()=>{const{getPrefixCls:e,iconPrefixCls:t}=p();return{iconPrefixCls:t,rootPrefixCls:e()}},useToken:()=>{const[e,t,n,r,o]=Zt();return{theme:e,realToken:t,hashId:n,token:r,cssVar:o}},useCSP:()=>{const{csp:e}=p();return e??{}},layer:{name:"antdx",dependencies:["antd"]}}),en=e=>{const{componentCls:t,calc:n}=e;return{[t]:{[`&${t}-rtl`]:{direction:"rtl"},[`${t}-list`]:{display:"inline-flex",flexDirection:"row",gap:e.paddingXS,color:e.colorTextDescription,"&-item, &-sub-item":{cursor:"pointer",padding:e.paddingXXS,borderRadius:e.borderRadius,height:e.controlHeightSM,width:e.controlHeightSM,boxSizing:"border-box",display:"inline-flex",alignItems:"center",justifyContent:"center","&-icon":{display:"inline-flex",alignItems:"center",justifyContent:"center",fontSize:e.fontSize,width:"100%",height:"100%"},"&:hover":{background:e.colorBgTextHover}}},"& .border":{padding:`${e.paddingXS} ${e.paddingSM}`,gap:e.paddingSM,borderRadius:n(e.borderRadiusLG).mul(1.5).equal(),backgroundColor:e.colorBorderSecondary,color:e.colorTextSecondary,[`${t}-list-item, ${t}-list-sub-item`]:{padding:0,lineHeight:e.lineHeight,"&-icon":{fontSize:e.fontSizeLG},"&:hover":{opacity:.8}}},"& .block":{display:"flex"}}}};var tn=Yt("Actions",(e=>{const t=Lt(e,{});return[en(t)]}),(()=>({})));var nn=e=>{const{prefixCls:t,rootClassName:o={},style:i={},variant:s="borderless",block:c=!1,onClick:d,items:f=[],...h}=e,g=(0,l.Z)(h,{attr:!0,aria:!0,data:!0}),{getPrefixCls:v,direction:y}=p(),b=v("actions",t),x=m("actions"),[S,w,C]=tn(b),E=a()(b,x.className,o,C,w,{[`${b}-rtl`]:"rtl"===y}),$={...x.style,...i},k=e=>{const{icon:t,label:o,key:i}=e;return u().createElement("div",{className:a()(`${b}-list-item`),onClick:t=>((e,t,n)=>{t.onItemClick?t.onItemClick(t):d?.({key:e,item:t,keyPath:[e],domEvent:n})})(i,e,t),key:i},(s=u().createElement("div",{className:`${b}-list-item-icon`},t),(l=o)?u().createElement(r.Tooltip,n()({},c,{title:l}),s):s));var s,l,c};return S(u().createElement("div",n()({className:E},g,{style:$}),u().createElement("div",{className:a()(`${b}-list`,s,c)},f.map((e=>"children"in e?u().createElement(We,{key:e.key,item:e,prefixCls:b,onClick:d}):k(e))))))},rn=i(314),on=i.n(rn);const an=u().createContext(null);function sn(e){const{getDropContainer:t,className:n,prefixCls:r,children:o}=e,{disabled:i}=u().useContext(an),[s,l]=u().useState(),[c,d]=u().useState(null);u().useEffect((()=>{const e=t?.();s!==e&&l(e)}),[t]),u().useEffect((()=>{if(s){const e=()=>{d(!0)},t=e=>{e.preventDefault()},n=e=>{e.relatedTarget||d(!1)},r=e=>{d(!1),e.preventDefault()};return document.addEventListener("dragenter",e),document.addEventListener("dragover",t),document.addEventListener("dragleave",n),document.addEventListener("drop",r),()=>{document.removeEventListener("dragenter",e),document.removeEventListener("dragover",t),document.removeEventListener("dragleave",n),document.removeEventListener("drop",r)}}}),[!!s]);if(!(t&&s&&!i))return null;const f=`${r}-drop-area`;return(0,rn.createPortal)(u().createElement("div",{className:a()(f,n,{[`${f}-on-body`]:"BODY"===s.tagName}),style:{display:c?"block":"none"}},o),s)}var ln={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},cn=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:ln}))};var un=c.forwardRef(cn),dn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"},fn=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:dn}))};var mn=c.forwardRef(fn),pn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"},hn=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:pn}))};var gn=c.forwardRef(hn);function vn(e){return e instanceof HTMLElement||e instanceof SVGElement}function yn(e){var t,n=function(e){return e&&"object"===b(e)&&vn(e.nativeElement)?e.nativeElement:vn(e)?e:null}(e);return n||(e instanceof u().Component?null===(t=on().findDOMNode)||void 0===t?void 0:t.call(on(),e):null)}var bn=c.createContext({});var xn=function(e){Je(n,e);var t=rt(n);function n(){return Ue(this,n),t.apply(this,arguments)}return Ze(n,[{key:"render",value:function(){return this.props.children}}]),n}(c.Component),Sn=xn;var wn="none",Cn="appear",En="enter",$n="leave",kn="none",Mn="prepare",Rn="start",Nn="active",Ln="end",zn="prepared";function Tn(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit".concat(e)]="webkit".concat(t),n["Moz".concat(e)]="moz".concat(t),n["ms".concat(e)]="MS".concat(t),n["O".concat(e)]="o".concat(t.toLowerCase()),n}var On,Hn,Pn,jn=(On=se(),Hn="undefined"!=typeof window?window:{},Pn={animationend:Tn("Animation","AnimationEnd"),transitionend:Tn("Transition","TransitionEnd")},On&&("AnimationEvent"in Hn||delete Pn.animationend.animation,"TransitionEvent"in Hn||delete Pn.transitionend.transition),Pn),_n={};if(se()){var Dn=document.createElement("div");_n=Dn.style}var In={};function An(e){if(In[e])return In[e];var t=jn[e];if(t)for(var n=Object.keys(t),r=n.length,o=0;o<r;o+=1){var a=n[o];if(Object.prototype.hasOwnProperty.call(t,a)&&a in _n)return In[e]=t[a],In[e]}return""}var Bn=An("animationend"),Xn=An("transitionend"),Fn=!(!Bn||!Xn),qn=Bn||"animationend",Vn=Xn||"transitionend";function Wn(e,t){return e?"object"===b(e)?e[t.replace(/-\w/g,(function(e){return e[1].toUpperCase()}))]:"".concat(e,"-").concat(t):null}var Gn=se()?c.useLayoutEffect:c.useEffect,Un=function(e){return+setTimeout(e,16)},Kn=function(e){return clearTimeout(e)};"undefined"!=typeof window&&"requestAnimationFrame"in window&&(Un=function(e){return window.requestAnimationFrame(e)},Kn=function(e){return window.cancelAnimationFrame(e)});var Zn=0,Yn=new Map;function Qn(e){Yn.delete(e)}var Jn=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=Zn+=1;function r(t){if(0===t)Qn(n),e();else{var o=Un((function(){r(t-1)}));Yn.set(n,o)}}return r(t),n};Jn.cancel=function(e){var t=Yn.get(e);return Qn(e),Kn(t)};var er=Jn,tr=[Mn,Rn,Nn,Ln],nr=[Mn,zn];function rr(e){return e===Nn||e===Ln}var or=function(e,t,n){var r=y(gt(kn),2),o=r[0],a=r[1],i=function(){var e=c.useRef(null);function t(){er.cancel(e.current)}return c.useEffect((function(){return function(){t()}}),[]),[function n(r){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;t();var a=er((function(){o<=1?r({isCanceled:function(){return a!==e.current}}):n(r,o-1)}));e.current=a},t]}(),s=y(i,2),l=s[0],u=s[1];var d=t?nr:tr;return Gn((function(){if(o!==kn&&o!==Ln){var e=d.indexOf(o),t=d[e+1],r=n(o);false===r?a(t,!0):t&&l((function(e){function n(){e.isCanceled()||a(t,!0)}!0===r?n():Promise.resolve(r).then(n)}))}}),[e,o]),c.useEffect((function(){return function(){u()}}),[]),[function(){a(Mn,!0)},o]};function ar(e,t,n,r){var o,a,i,s=r.motionEnter,l=void 0===s||s,u=r.motionAppear,d=void 0===u||u,f=r.motionLeave,m=void 0===f||f,p=r.motionDeadline,h=r.motionLeaveImmediately,g=r.onAppearPrepare,v=r.onEnterPrepare,b=r.onLeavePrepare,x=r.onAppearStart,w=r.onEnterStart,C=r.onLeaveStart,E=r.onAppearActive,$=r.onEnterActive,k=r.onLeaveActive,M=r.onAppearEnd,R=r.onEnterEnd,N=r.onLeaveEnd,L=r.onVisibleChanged,z=y(gt(),2),T=z[0],O=z[1],H=(o=wn,a=y(c.useReducer((function(e){return e+1}),0),2)[1],i=c.useRef(o),[ft((function(){return i.current})),ft((function(e){i.current="function"==typeof e?e(i.current):e,a()}))]),P=y(H,2),j=P[0],_=P[1],D=y(gt(null),2),I=D[0],A=D[1],B=j(),X=(0,c.useRef)(!1),F=(0,c.useRef)(null);function q(){return n()}var V=(0,c.useRef)(!1);function W(){_(wn),A(null,!0)}var G=ft((function(e){var t=j();if(t!==wn){var n=q();if(!e||e.deadline||e.target===n){var r,o=V.current;t===Cn&&o?r=null==M?void 0:M(n,e):t===En&&o?r=null==R?void 0:R(n,e):t===$n&&o&&(r=null==N?void 0:N(n,e)),o&&!1!==r&&W()}}})),U=function(e){var t=(0,c.useRef)();function n(t){t&&(t.removeEventListener(Vn,e),t.removeEventListener(qn,e))}return c.useEffect((function(){return function(){n(t.current)}}),[]),[function(r){t.current&&t.current!==r&&n(t.current),r&&r!==t.current&&(r.addEventListener(Vn,e),r.addEventListener(qn,e),t.current=r)},n]}(G),K=y(U,1)[0],Z=function(e){switch(e){case Cn:return S(S(S({},Mn,g),Rn,x),Nn,E);case En:return S(S(S({},Mn,v),Rn,w),Nn,$);case $n:return S(S(S({},Mn,b),Rn,C),Nn,k);default:return{}}},Y=c.useMemo((function(){return Z(B)}),[B]),Q=y(or(B,!e,(function(e){if(e===Mn){var t=Y[Mn];return!!t&&t(q())}var n;ee in Y&&A((null===(n=Y[ee])||void 0===n?void 0:n.call(Y,q(),null))||null);return ee===Nn&&B!==wn&&(K(q()),p>0&&(clearTimeout(F.current),F.current=setTimeout((function(){G({deadline:!0})}),p))),ee===zn&&W(),true})),2),J=Q[0],ee=Q[1],te=rr(ee);V.current=te;var ne=(0,c.useRef)(null);Gn((function(){if(!X.current||ne.current!==t){O(t);var n,r=X.current;X.current=!0,!r&&t&&d&&(n=Cn),r&&t&&l&&(n=En),(r&&!t&&m||!r&&h&&!t&&m)&&(n=$n);var o=Z(n);n&&(e||o[Mn])?(_(n),J()):_(wn),ne.current=t}}),[t]),(0,c.useEffect)((function(){(B===Cn&&!d||B===En&&!l||B===$n&&!m)&&_(wn)}),[d,l,m]),(0,c.useEffect)((function(){return function(){X.current=!1,clearTimeout(F.current)}}),[]);var re=c.useRef(!1);(0,c.useEffect)((function(){T&&(re.current=!0),void 0!==T&&B===wn&&((re.current||T)&&(null==L||L(T)),re.current=!0)}),[T,B]);var oe=I;return Y[Mn]&&ee===Rn&&(oe=ie({transition:"none"},oe)),[B,ee,oe,null!=T?T:t]}var ir=function(e){var t=e;"object"===b(e)&&(t=e.transitionSupport);var n=c.forwardRef((function(e,n){var r=e.visible,o=void 0===r||r,i=e.removeOnLeave,s=void 0===i||i,l=e.forceRender,u=e.children,d=e.motionName,f=e.leavedClassName,m=e.eventProps,p=function(e,n){return!(!e.motionName||!t||!1===n)}(e,c.useContext(bn).motion),h=(0,c.useRef)(),g=(0,c.useRef)();var v=y(ar(p,o,(function(){try{return h.current instanceof HTMLElement?h.current:yn(g.current)}catch(e){return null}}),e),4),b=v[0],x=v[1],w=v[2],C=v[3],E=c.useRef(C);C&&(E.current=!0);var $,k=c.useCallback((function(e){h.current=e,Et(n,e)}),[n]),M=ie(ie({},m),{},{visible:o});if(u)if(b===wn)$=C?u(ie({},M),k):!s&&E.current&&f?u(ie(ie({},M),{},{className:f}),k):l||!s&&!f?u(ie(ie({},M),{},{style:{display:"none"}}),k):null;else{var R;x===Mn?R="prepare":rr(x)?R="active":x===Rn&&(R="start");var N=Wn(d,"".concat(b,"-").concat(R));$=u(ie(ie({},M),{},{className:a()(Wn(d,b),S(S({},N,N&&R),d,"string"==typeof d)),style:w}),k)}else $=null;c.isValidElement($)&&$t($)&&(function(e){if(e&&kt(e)){var t=e;return t.props.propertyIsEnumerable("ref")?t.props.ref:t.ref}return null}($)||($=c.cloneElement($,{ref:k})));return c.createElement(Sn,{ref:g},$)}));return n.displayName="CSSMotion",n}(Fn),sr="add",lr="keep",cr="remove",ur="removed";function dr(e){var t;return ie(ie({},t=e&&"object"===b(e)&&"key"in e?e:{key:e}),{},{key:String(t.key)})}function fr(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map(dr)}function mr(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[],r=0,o=t.length,a=fr(e),i=fr(t);a.forEach((function(e){for(var t=!1,a=r;a<o;a+=1){var s=i[a];if(s.key===e.key){r<a&&(n=n.concat(i.slice(r,a).map((function(e){return ie(ie({},e),{},{status:sr})}))),r=a),n.push(ie(ie({},s),{},{status:lr})),r+=1,t=!0;break}}t||n.push(ie(ie({},e),{},{status:cr}))})),r<o&&(n=n.concat(i.slice(r).map((function(e){return ie(ie({},e),{},{status:sr})}))));var s={};n.forEach((function(e){var t=e.key;s[t]=(s[t]||0)+1}));var l=Object.keys(s).filter((function(e){return s[e]>1}));return l.forEach((function(e){(n=n.filter((function(t){var n=t.key,r=t.status;return n!==e||r!==cr}))).forEach((function(t){t.key===e&&(t.status=lr)}))})),n}var pr=["component","children","onVisibleChanged","onAllRemoved"],hr=["status"],gr=["eventProps","visible","children","motionName","motionAppear","motionEnter","motionLeave","motionLeaveImmediately","motionDeadline","removeOnLeave","leavedClassName","onAppearPrepare","onAppearStart","onAppearActive","onAppearEnd","onEnterStart","onEnterActive","onEnterEnd","onLeaveStart","onLeaveActive","onLeaveEnd"];var vr=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ir,n=function(e){Je(r,e);var n=rt(r);function r(){var e;Ue(this,r);for(var t=arguments.length,o=new Array(t),a=0;a<t;a++)o[a]=arguments[a];return S(Ye(e=n.call.apply(n,[this].concat(o))),"state",{keyEntities:[]}),S(Ye(e),"removeKey",(function(t){e.setState((function(e){return{keyEntities:e.keyEntities.map((function(e){return e.key!==t?e:ie(ie({},e),{},{status:ur})}))}}),(function(){0===e.state.keyEntities.filter((function(e){return e.status!==ur})).length&&e.props.onAllRemoved&&e.props.onAllRemoved()}))})),e}return Ze(r,[{key:"render",value:function(){var e=this,n=this.state.keyEntities,r=this.props,o=r.component,a=r.children,i=r.onVisibleChanged,s=(r.onAllRemoved,w(r,pr)),l=o||c.Fragment,u={};return gr.forEach((function(e){u[e]=s[e],delete s[e]})),delete s.keys,c.createElement(l,s,n.map((function(n,r){var o=n.status,s=w(n,hr),l=o===sr||o===lr;return c.createElement(t,h({},u,{key:s.key,visible:l,eventProps:s,onVisibleChanged:function(t){null==i||i(t,{key:s.key}),t||e.removeKey(s.key)}}),(function(e,t){return a(ie(ie({},e),{},{index:r}),t)}))})))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.keys,r=t.keyEntities,o=fr(n);return{keyEntities:mr(r,o).filter((function(e){var t=r.find((function(t){var n=t.key;return e.key===n}));return!t||t.status!==ur||e.status!==cr}))}}}]),r}(c.Component);return S(n,"defaultProps",{component:"div"}),n}(Fn),yr=ir;function br(e,t){const{children:o,upload:a,rootClassName:i}=e,s=u().useRef(null);return u().useImperativeHandle(t,(()=>s.current)),u().createElement(r.Upload,n()({},a,{showUploadList:!1,rootClassName:i,ref:s}),o)}var xr=u().forwardRef(br),Sr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM575.34 477.84l-61.22 102.3L452.3 477.8a12 12 0 00-10.27-5.79h-38.44a12 12 0 00-6.4 1.85 12 12 0 00-3.75 16.56l82.34 130.42-83.45 132.78a12 12 0 00-1.84 6.39 12 12 0 0012 12h34.46a12 12 0 0010.21-5.7l62.7-101.47 62.3 101.45a12 12 0 0010.23 5.72h37.48a12 12 0 006.48-1.9 12 12 0 003.62-16.58l-83.83-130.55 85.3-132.47a12 12 0 001.9-6.5 12 12 0 00-12-12h-35.7a12 12 0 00-10.29 5.84z"}}]},name:"file-excel",theme:"filled"},wr=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:Sr}))};var Cr=c.forwardRef(wr),Er={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7L639.4 73.4c-6-6-14.2-9.4-22.7-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.6-9.4-22.6zM400 402c22.1 0 40 17.9 40 40s-17.9 40-40 40-40-17.9-40-40 17.9-40 40-40zm296 294H328c-6.7 0-10.4-7.7-6.3-12.9l99.8-127.2a8 8 0 0112.6 0l41.1 52.4 77.8-99.2a8 8 0 0112.6 0l136.5 174c4.3 5.2.5 12.9-6.1 12.9zm-94-370V137.8L790.2 326H602z"}}]},name:"file-image",theme:"filled"},$r=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:Er}))};var kr=c.forwardRef($r),Mr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM426.13 600.93l59.11 132.97a16 16 0 0014.62 9.5h24.06a16 16 0 0014.63-9.51l59.1-133.35V758a16 16 0 0016.01 16H641a16 16 0 0016-16V486a16 16 0 00-16-16h-34.75a16 16 0 00-14.67 9.62L512.1 662.2l-79.48-182.59a16 16 0 00-14.67-9.61H383a16 16 0 00-16 16v272a16 16 0 0016 16h27.13a16 16 0 0016-16V600.93z"}}]},name:"file-markdown",theme:"filled"},Rr=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:Mr}))};var Nr=c.forwardRef(Rr),Lr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM633.22 637.26c-15.18-.5-31.32.67-49.65 2.96-24.3-14.99-40.66-35.58-52.28-65.83l1.07-4.38 1.24-5.18c4.3-18.13 6.61-31.36 7.3-44.7.52-10.07-.04-19.36-1.83-27.97-3.3-18.59-16.45-29.46-33.02-30.13-15.45-.63-29.65 8-33.28 21.37-5.91 21.62-2.45 50.07 10.08 98.59-15.96 38.05-37.05 82.66-51.2 107.54-18.89 9.74-33.6 18.6-45.96 28.42-16.3 12.97-26.48 26.3-29.28 40.3-1.36 6.49.69 14.97 5.36 21.92 5.3 7.88 13.28 13 22.85 13.74 24.15 1.87 53.83-23.03 86.6-79.26 3.29-1.1 6.77-2.26 11.02-3.7l11.9-4.02c7.53-2.54 12.99-4.36 18.39-6.11 23.4-7.62 41.1-12.43 57.2-15.17 27.98 14.98 60.32 24.8 82.1 24.8 17.98 0 30.13-9.32 34.52-23.99 3.85-12.88.8-27.82-7.48-36.08-8.56-8.41-24.3-12.43-45.65-13.12zM385.23 765.68v-.36l.13-.34a54.86 54.86 0 015.6-10.76c4.28-6.58 10.17-13.5 17.47-20.87 3.92-3.95 8-7.8 12.79-12.12 1.07-.96 7.91-7.05 9.19-8.25l11.17-10.4-8.12 12.93c-12.32 19.64-23.46 33.78-33 43-3.51 3.4-6.6 5.9-9.1 7.51a16.43 16.43 0 01-2.61 1.42c-.41.17-.77.27-1.13.3a2.2 2.2 0 01-1.12-.15 2.07 2.07 0 01-1.27-1.91zM511.17 547.4l-2.26 4-1.4-4.38c-3.1-9.83-5.38-24.64-6.01-38-.72-15.2.49-24.32 5.29-24.32 6.74 0 9.83 10.8 10.07 27.05.22 14.28-2.03 29.14-5.7 35.65zm-5.81 58.46l1.53-4.05 2.09 3.8c11.69 21.24 26.86 38.96 43.54 51.31l3.6 2.66-4.39.9c-16.33 3.38-31.54 8.46-52.34 16.85 2.17-.88-21.62 8.86-27.64 11.17l-5.25 2.01 2.8-4.88c12.35-21.5 23.76-47.32 36.05-79.77zm157.62 76.26c-7.86 3.1-24.78.33-54.57-12.39l-7.56-3.22 8.2-.6c23.3-1.73 39.8-.45 49.42 3.07 4.1 1.5 6.83 3.39 8.04 5.55a4.64 4.64 0 01-1.36 6.31 6.7 6.7 0 01-2.17 1.28z"}}]},name:"file-pdf",theme:"filled"},zr=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:Lr}))};var Tr=c.forwardRef(zr),Or={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM468.53 760v-91.54h59.27c60.57 0 100.2-39.65 100.2-98.12 0-58.22-39.58-98.34-99.98-98.34H424a12 12 0 00-12 12v276a12 12 0 0012 12h32.53a12 12 0 0012-12zm0-139.33h34.9c47.82 0 67.19-12.93 67.19-50.33 0-32.05-18.12-50.12-49.87-50.12h-52.22v100.45z"}}]},name:"file-ppt",theme:"filled"},Hr=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:Or}))};var Pr=c.forwardRef(Hr),jr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM512 566.1l52.81 197a12 12 0 0011.6 8.9h31.77a12 12 0 0011.6-8.88l74.37-276a12 12 0 00.4-3.12 12 12 0 00-12-12h-35.57a12 12 0 00-11.7 9.31l-45.78 199.1-49.76-199.32A12 12 0 00528.1 472h-32.2a12 12 0 00-11.64 9.1L434.6 680.01 388.5 481.3a12 12 0 00-11.68-9.29h-35.39a12 12 0 00-3.11.41 12 12 0 00-8.47 14.7l74.17 276A12 12 0 00415.6 772h31.99a12 12 0 0011.59-8.9l52.81-197z"}}]},name:"file-word",theme:"filled"},_r=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:jr}))};var Dr=c.forwardRef(_r),Ir={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM296 136v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm64 64v64h64v-64h-64zm-64 64v64h64v-64h-64zm0 64v160h128V584H296zm48 48h32v64h-32v-64z"}}]},name:"file-zip",theme:"filled"},Ar=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:Ir}))};var Br=c.forwardRef(Ar),Xr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.7c6 6 9.4 14.1 9.4 22.6V928c0 17.7-14.3 32-32 32H192c-17.7 0-32-14.3-32-32V96c0-17.7 14.3-32 32-32h424.7c8.5 0 16.7 3.4 22.7 9.4l215.2 215.3zM790.2 326L602 137.8V326h188.2zM320 482a8 8 0 00-8 8v48a8 8 0 008 8h384a8 8 0 008-8v-48a8 8 0 00-8-8H320zm0 136a8 8 0 00-8 8v48a8 8 0 008 8h184a8 8 0 008-8v-48a8 8 0 00-8-8H320z"}}]},name:"file-text",theme:"filled"},Fr=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:Xr}))};var qr=c.forwardRef(Fr),Vr={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"}}]},name:"close-circle",theme:"filled"},Wr=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:Vr}))};var Gr=c.forwardRef(Wr);var Ur=e=>{const{componentCls:t,antCls:n,calc:r}=e,o=`${t}-list-card`,a=r(e.fontSize).mul(e.lineHeight).mul(2).add(e.paddingSM).add(e.paddingSM).equal();return{[o]:{borderRadius:e.borderRadius,position:"relative",background:e.colorFillContent,borderWidth:e.lineWidth,borderStyle:"solid",borderColor:"transparent",flex:"none",[`${o}-name,${o}-desc`]:{display:"flex",flexWrap:"nowrap",maxWidth:"100%"},[`${o}-ellipsis-prefix`]:{flex:"0 1 auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},[`${o}-ellipsis-suffix`]:{flex:"none"},"&-type-overview":{padding:r(e.paddingSM).sub(e.lineWidth).equal(),paddingInlineStart:r(e.padding).add(e.lineWidth).equal(),display:"flex",flexWrap:"nowrap",gap:e.paddingXS,alignItems:"flex-start",width:236,[`${o}-icon`]:{fontSize:r(e.fontSizeLG).mul(2).equal(),lineHeight:1,paddingTop:r(e.paddingXXS).mul(1.5).equal(),flex:"none"},[`${o}-content`]:{flex:"auto",minWidth:0,display:"flex",flexDirection:"column",alignItems:"stretch"},[`${o}-desc`]:{color:e.colorTextTertiary}},"&-type-preview":{width:a,height:a,lineHeight:1,display:"flex",alignItems:"center",[`&:not(${o}-status-error)`]:{border:0},[`${n}-image`]:{width:"100%",height:"100%",borderRadius:"inherit",position:"relative",overflow:"hidden",img:{height:"100%",objectFit:"cover",borderRadius:"inherit"}},[`${o}-img-mask`]:{position:"absolute",inset:0,display:"flex",justifyContent:"center",alignItems:"center",borderRadius:"inherit",background:`rgba(0, 0, 0, ${e.opacityLoading})`},[`&${o}-status-error`]:{borderRadius:"inherit",[`img, ${o}-img-mask`]:{borderRadius:r(e.borderRadius).sub(e.lineWidth).equal()},[`${o}-desc`]:{paddingInline:e.paddingXXS}},[`${o}-progress`]:{}},[`${o}-remove`]:{position:"absolute",top:0,insetInlineEnd:0,border:0,padding:e.paddingXXS,background:"transparent",lineHeight:1,transform:"translate(50%, -50%)",fontSize:e.fontSize,cursor:"pointer",opacity:e.opacityLoading,display:"none","&:dir(rtl)":{transform:"translate(-50%, -50%)"},"&:hover":{opacity:1},"&:active":{opacity:e.opacityLoading}},[`&:hover ${o}-remove`]:{display:"block"},"&-status-error":{borderColor:e.colorError,[`${o}-desc`]:{color:e.colorError}},"&-motion":{transition:["opacity","width","margin","padding"].map((t=>`${t} ${e.motionDurationSlow}`)).join(","),"&-appear-start":{width:0,transition:"none"},"&-leave-active":{opacity:0,width:0,paddingInline:0,borderInlineWidth:0,marginInlineEnd:r(e.paddingSM).mul(-1).equal()}}}}};const Kr={"&, *":{boxSizing:"border-box"}},Zr=e=>{const{componentCls:t,calc:n,antCls:r}=e,o=`${t}-placeholder`;return{[`${t}-drop-area`]:{position:"absolute",inset:0,zIndex:e.zIndexPopupBase,...Kr,"&-on-body":{position:"fixed",inset:0},"&-hide-placement":{[`${o}-inner`]:{display:"none"}},[o]:{padding:0}},"&":{[o]:{height:"100%",borderRadius:e.borderRadius,borderWidth:e.lineWidthBold,borderStyle:"dashed",borderColor:"transparent",padding:e.padding,position:"relative",backdropFilter:"blur(10px)",background:e.colorBgPlaceholderHover,...Kr,[`${r}-upload-wrapper ${r}-upload${r}-upload-btn`]:{padding:0},[`&${o}-drag-in`]:{borderColor:e.colorPrimaryHover},[`&${o}-disabled`]:{opacity:.25,pointerEvents:"none"},[`${o}-inner`]:{gap:n(e.paddingXXS).div(2).equal()},[`${o}-icon`]:{fontSize:e.fontSizeHeading2,lineHeight:1},[`${o}-title${o}-title`]:{margin:0,fontSize:e.fontSize,lineHeight:e.lineHeight},[`${o}-description`]:{}}}}},Yr=e=>{const{componentCls:t,calc:n}=e,r=`${t}-list`,o=n(e.fontSize).mul(e.lineHeight).mul(2).add(e.paddingSM).add(e.paddingSM).equal();return{[t]:{position:"relative",width:"100%",...Kr,[r]:{display:"flex",flexWrap:"wrap",gap:e.paddingSM,fontSize:e.fontSize,lineHeight:e.lineHeight,color:e.colorText,paddingBlock:e.paddingSM,paddingInline:e.padding,width:"100%",background:e.colorBgContainer,scrollbarWidth:"none","-ms-overflow-style":"none","&::-webkit-scrollbar":{display:"none"},"&-overflow-scrollX, &-overflow-scrollY":{"&:before, &:after":{content:'""',position:"absolute",opacity:0,transition:`opacity ${e.motionDurationSlow}`,zIndex:1}},"&-overflow-ping-start:before":{opacity:1},"&-overflow-ping-end:after":{opacity:1},"&-overflow-scrollX":{overflowX:"auto",overflowY:"hidden",flexWrap:"nowrap","&:before, &:after":{insetBlock:0,width:8},"&:before":{insetInlineStart:0,background:"linear-gradient(to right, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{insetInlineEnd:0,background:"linear-gradient(to left, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:dir(rtl)":{"&:before":{background:"linear-gradient(to left, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{background:"linear-gradient(to right, rgba(0,0,0,0.06), rgba(0,0,0,0));"}}},"&-overflow-scrollY":{overflowX:"hidden",overflowY:"auto",maxHeight:n(o).mul(3).equal(),"&:before, &:after":{insetInline:0,height:8},"&:before":{insetBlockStart:0,background:"linear-gradient(to bottom, rgba(0,0,0,0.06), rgba(0,0,0,0));"},"&:after":{insetBlockEnd:0,background:"linear-gradient(to top, rgba(0,0,0,0.06), rgba(0,0,0,0));"}},"&-upload-btn":{width:o,height:o,fontSize:e.fontSizeHeading2,color:"#999"},"&-prev-btn, &-next-btn":{position:"absolute",top:"50%",transform:"translateY(-50%)",boxShadow:e.boxShadowTertiary,opacity:0,pointerEvents:"none"},"&-prev-btn":{left:{_skip_check_:!0,value:e.padding}},"&-next-btn":{right:{_skip_check_:!0,value:e.padding}},"&:dir(ltr)":{[`&${r}-overflow-ping-start ${r}-prev-btn`]:{opacity:1,pointerEvents:"auto"},[`&${r}-overflow-ping-end ${r}-next-btn`]:{opacity:1,pointerEvents:"auto"}},"&:dir(rtl)":{[`&${r}-overflow-ping-end ${r}-prev-btn`]:{opacity:1,pointerEvents:"auto"},[`&${r}-overflow-ping-start ${r}-next-btn`]:{opacity:1,pointerEvents:"auto"}}}}}};var Qr=Yt("Attachments",(e=>{const t=Lt(e,{});return[Zr(t),Yr(t),Ur(t)]}),(e=>{const{colorBgContainer:t}=e;return{colorBgPlaceholderHover:new M(t).setA(.85).toRgbString()}}));const Jr=200;function eo(){return u().createElement("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},u().createElement("title",null,"audio"),u().createElement("g",{stroke:"none",strokeWidth:"1",fill:"none",fillRule:"evenodd"},u().createElement("path",{d:"M14.1178571,4.0125 C14.225,4.11964286 14.2857143,4.26428571 14.2857143,4.41607143 L14.2857143,15.4285714 C14.2857143,15.7446429 14.0303571,16 13.7142857,16 L2.28571429,16 C1.96964286,16 1.71428571,15.7446429 1.71428571,15.4285714 L1.71428571,0.571428571 C1.71428571,0.255357143 1.96964286,0 2.28571429,0 L9.86964286,0 C10.0214286,0 10.1678571,0.0607142857 10.275,0.167857143 L14.1178571,4.0125 Z M10.7315824,7.11216117 C10.7428131,7.15148751 10.7485063,7.19218979 10.7485063,7.23309113 L10.7485063,8.07742614 C10.7484199,8.27364959 10.6183424,8.44607275 10.4296853,8.50003683 L8.32984514,9.09986306 L8.32984514,11.7071803 C8.32986605,12.5367078 7.67249692,13.217028 6.84345686,13.2454634 L6.79068592,13.2463395 C6.12766108,13.2463395 5.53916361,12.8217001 5.33010655,12.1924966 C5.1210495,11.563293 5.33842118,10.8709227 5.86959669,10.4741173 C6.40077221,10.0773119 7.12636292,10.0652587 7.67042486,10.4442027 L7.67020842,7.74937024 L7.68449368,7.74937024 C7.72405122,7.59919041 7.83988806,7.48101083 7.98924584,7.4384546 L10.1880418,6.81004755 C10.42156,6.74340323 10.6648954,6.87865515 10.7315824,7.11216117 Z M9.60714286,1.31785714 L12.9678571,4.67857143 L9.60714286,4.67857143 L9.60714286,1.31785714 Z",fill:"currentColor"})))}function to(e){const{percent:t}=e,{token:n}=r.theme.useToken();return u().createElement(r.Progress,{type:"circle",percent:t,size:2*n.fontSizeHeading2,strokeColor:"#FFF",trailColor:"rgba(255, 255, 255, 0.3)",format:e=>u().createElement("span",{style:{color:"#FFF"}},(e||0).toFixed(0),"%")})}function no(){return u().createElement("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},u().createElement("title",null,"video"),u().createElement("g",{stroke:"none",strokeWidth:"1",fill:"none",fillRule:"evenodd"},u().createElement("path",{d:"M14.1178571,4.0125 C14.225,4.11964286 14.2857143,4.26428571 14.2857143,4.41607143 L14.2857143,15.4285714 C14.2857143,15.7446429 14.0303571,16 13.7142857,16 L2.28571429,16 C1.96964286,16 1.71428571,15.7446429 1.71428571,15.4285714 L1.71428571,0.571428571 C1.71428571,0.255357143 1.96964286,0 2.28571429,0 L9.86964286,0 C10.0214286,0 10.1678571,0.0607142857 10.275,0.167857143 L14.1178571,4.0125 Z M12.9678571,4.67857143 L9.60714286,1.31785714 L9.60714286,4.67857143 L12.9678571,4.67857143 Z M10.5379461,10.3101106 L6.68957555,13.0059749 C6.59910784,13.0693494 6.47439406,13.0473861 6.41101953,12.9569184 C6.3874624,12.9232903 6.37482581,12.8832269 6.37482581,12.8421686 L6.37482581,7.45043999 C6.37482581,7.33998304 6.46436886,7.25043999 6.57482581,7.25043999 C6.61588409,7.25043999 6.65594753,7.26307658 6.68957555,7.28663371 L10.5379461,9.98249803 C10.6284138,10.0458726 10.6503772,10.1705863 10.5870027,10.2610541 C10.5736331,10.2801392 10.5570312,10.2967411 10.5379461,10.3101106 Z",fill:"currentColor"})))}const ro="#8c8c8c",oo=["png","jpg","jpeg","gif","bmp","webp","svg"],ao=[{icon:u().createElement(Cr,null),color:"#22b35e",ext:["xlsx","xls"]},{icon:u().createElement(kr,null),color:ro,ext:oo},{icon:u().createElement(Nr,null),color:ro,ext:["md","mdx"]},{icon:u().createElement(Tr,null),color:"#ff4d4f",ext:["pdf"]},{icon:u().createElement(Pr,null),color:"#ff6e31",ext:["ppt","pptx"]},{icon:u().createElement(Dr,null),color:"#1677ff",ext:["doc","docx"]},{icon:u().createElement(Br,null),color:"#fab714",ext:["zip","rar","7z","tar","gz"]},{icon:u().createElement(no,null),color:"#ff4d4f",ext:["mp4","avi","mov","wmv","flv","mkv"]},{icon:u().createElement(eo,null),color:"#8c8c8c",ext:["mp3","wav","flac","ape","aac","ogg"]}];function io(e,t){return t.some((t=>e.toLowerCase()===`.${t}`))}function so(e,t){const{prefixCls:o,item:i,onRemove:s,className:l,style:c,imageProps:d}=e,f=u().useContext(an),{disabled:m}=f||{},{name:h,size:g,percent:v,status:y="done",description:b}=i,{getPrefixCls:x}=p(),S=x("attachment",o),w=`${S}-list-card`,[C,E,$]=Qr(S),[k,M]=u().useMemo((()=>{const e=h||"",t=e.match(/^(.*)\.[^.]+$/);return t?[t[1],e.slice(t[1].length)]:[e,""]}),[h]),R=u().useMemo((()=>io(M,oo)),[M]),N=u().useMemo((()=>b||("uploading"===y?`${v||0}%`:"error"===y?i.response||" ":g?function(e){let t=e;const n=["B","KB","MB","GB","TB","PB","EB"];let r=0;for(;t>=1024&&r<n.length-1;)t/=1024,r++;return`${t.toFixed(0)} ${n[r]}`}(g):" ")),[y,v]),[L,z]=u().useMemo((()=>{for(const{ext:e,icon:t,color:n}of ao)if(io(M,e))return[t,n];return[u().createElement(qr,{key:"defaultIcon"}),ro]}),[M]),[T,O]=u().useState();u().useEffect((()=>{if(i.originFileObj){let t=!0;return(e=i.originFileObj,new Promise((t=>{if(!e||!e.type||0!==e.type.indexOf("image/"))return void t("");const n=new Image;if(n.onload=()=>{const{width:e,height:r}=n,o=e/r,a=o>1?Jr:Jr*o,i=o>1?Jr/o:Jr,s=document.createElement("canvas");s.width=a,s.height=i,s.style.cssText=`position: fixed; left: 0; top: 0; width: ${a}px; height: ${i}px; z-index: 9999; display: none;`,document.body.appendChild(s),s.getContext("2d").drawImage(n,0,0,a,i);const l=s.toDataURL();document.body.removeChild(s),window.URL.revokeObjectURL(n.src),t(l)},n.crossOrigin="anonymous",e.type.startsWith("image/svg+xml")){const t=new FileReader;t.onload=()=>{t.result&&"string"==typeof t.result&&(n.src=t.result)},t.readAsDataURL(e)}else if(e.type.startsWith("image/gif")){const n=new FileReader;n.onload=()=>{n.result&&t(n.result)},n.readAsDataURL(e)}else n.src=window.URL.createObjectURL(e)}))).then((e=>{t&&O(e)})),()=>{t=!1}}var e;O(void 0)}),[i.originFileObj]);let H=null;const P=i.thumbUrl||i.url||T,j=R&&(i.originFileObj||P);return H=j?u().createElement(u().Fragment,null,P&&u().createElement(r.Image,n()({alt:"preview",src:P},d)),"done"!==y&&u().createElement("div",{className:`${w}-img-mask`},"uploading"===y&&void 0!==v&&u().createElement(to,{percent:v,prefixCls:w}),"error"===y&&u().createElement("div",{className:`${w}-desc`},u().createElement("div",{className:`${w}-ellipsis-prefix`},N)))):u().createElement(u().Fragment,null,u().createElement("div",{className:`${w}-icon`,style:{color:z}},L),u().createElement("div",{className:`${w}-content`},u().createElement("div",{className:`${w}-name`},u().createElement("div",{className:`${w}-ellipsis-prefix`},k??" "),u().createElement("div",{className:`${w}-ellipsis-suffix`},M)),u().createElement("div",{className:`${w}-desc`},u().createElement("div",{className:`${w}-ellipsis-prefix`},N)))),C(u().createElement("div",{className:a()(w,{[`${w}-status-${y}`]:y,[`${w}-type-preview`]:j,[`${w}-type-overview`]:!j},l,E,$),style:c,ref:t},H,!m&&s&&u().createElement("button",{type:"button",className:`${w}-remove`,onClick:()=>{s(i)}},u().createElement(Gr,null))))}var lo=u().forwardRef(so);function co(e){const{prefixCls:t,items:n,onRemove:o,overflow:i,upload:s,listClassName:l,listStyle:c,itemClassName:d,uploadClassName:f,uploadStyle:m,itemStyle:p,imageProps:h}=e,g=`${t}-list`,v=u().useRef(null),[y,b]=u().useState(!1),{disabled:x}=u().useContext(an);u().useEffect((()=>(b(!0),()=>{b(!1)})),[]);const[S,w]=u().useState(!1),[C,E]=u().useState(!1),$=()=>{const e=v.current;e&&("scrollX"===i?(w(Math.abs(e.scrollLeft)>=1),E(e.scrollWidth-e.clientWidth-Math.abs(e.scrollLeft)>=1)):"scrollY"===i&&(w(0!==e.scrollTop),E(e.scrollHeight-e.clientHeight!==e.scrollTop)))};u().useEffect((()=>{$()}),[i,n.length]);const k=e=>{const t=v.current;t&&t.scrollTo({left:t.scrollLeft+e*t.clientWidth,behavior:"smooth"})};return u().createElement("div",{className:a()(g,{[`${g}-overflow-${e.overflow}`]:i,[`${g}-overflow-ping-start`]:S,[`${g}-overflow-ping-end`]:C},l),ref:v,onScroll:$,style:c},u().createElement(vr,{keys:n.map((e=>({key:e.uid,item:e}))),motionName:`${g}-card-motion`,component:!1,motionAppear:y,motionLeave:!0,motionEnter:!0},(({key:e,item:n,className:r,style:i})=>u().createElement(lo,{key:e,prefixCls:t,item:n,onRemove:o,className:a()(r,d),imageProps:h,style:{...i,...p}}))),!x&&u().createElement(xr,{upload:s},u().createElement(r.Button,{className:a()(f,`${g}-upload-btn`),style:m,type:"dashed"},u().createElement(un,{className:`${g}-upload-btn-icon`}))),"scrollX"===i&&u().createElement(u().Fragment,null,u().createElement(r.Button,{size:"small",shape:"circle",className:`${g}-prev-btn`,icon:u().createElement(mn,null),onClick:()=>{k(-1)}}),u().createElement(r.Button,{size:"small",shape:"circle",className:`${g}-next-btn`,icon:u().createElement(gn,null),onClick:()=>{k(1)}})))}function uo(e,t){const{prefixCls:o,placeholder:i={},upload:s,className:l,style:c}=e,d=`${o}-placeholder`,f=i||{},{disabled:m}=u().useContext(an),[p,h]=u().useState(!1),g=u().isValidElement(i)?i:u().createElement(r.Flex,{align:"center",justify:"center",vertical:!0,className:`${d}-inner`},u().createElement(r.Typography.Text,{className:`${d}-icon`},f.icon),u().createElement(r.Typography.Title,{className:`${d}-title`,level:5},f.title),u().createElement(r.Typography.Text,{className:`${d}-description`,type:"secondary"},f.description));return u().createElement("div",{className:a()(d,{[`${d}-drag-in`]:p,[`${d}-disabled`]:m},l),onDragEnter:()=>{h(!0)},onDragLeave:e=>{e.currentTarget.contains(e.relatedTarget)||h(!1)},onDrop:()=>{h(!1)},"aria-hidden":m,style:c},u().createElement(r.Upload.Dragger,n()({showUploadList:!1},s,{ref:t,style:{padding:0,border:0,background:"transparent"}}),g))}var fo=u().forwardRef(uo);function mo(e,t){const{prefixCls:n,rootClassName:r,rootStyle:o,className:i,style:s,items:l,children:c,getDropContainer:d,placeholder:f,onChange:h,onRemove:g,overflow:v,imageProps:y,disabled:b,classNames:x={},styles:S={},...w}=e,{getPrefixCls:C,direction:E}=p(),$=C("attachment",n),k=m("attachments"),{classNames:M,styles:R}=k,N=u().useRef(null),L=u().useRef(null);u().useImperativeHandle(t,(()=>({nativeElement:N.current,upload:e=>{const t=L.current?.nativeElement?.querySelector('input[type="file"]');if(t){const n=new DataTransfer;n.items.add(e),t.files=n.files,t.dispatchEvent(new Event("change",{bubbles:!0}))}}})));const[z,T,O]=Qr($),H=a()(T,O),[P,j]=yt([],{value:l}),_=ft((e=>{j(e.fileList),h?.(e)})),D={...w,fileList:P,onChange:_},I=e=>Promise.resolve("function"==typeof g?g(e):g).then((t=>{if(!1===t)return;const n=P.filter((t=>t.uid!==e.uid));_({file:{...e,status:"removed"},fileList:n})}));let A;const B=(e,t,n)=>{const r="function"==typeof f?f(e):f;return u().createElement(fo,{placeholder:r,upload:D,prefixCls:$,className:a()(M.placeholder,x.placeholder),style:{...R.placeholder,...S.placeholder,...t?.style},ref:n})};if(c)A=u().createElement(u().Fragment,null,u().createElement(xr,{upload:D,rootClassName:r,ref:L},c),u().createElement(sn,{getDropContainer:d,prefixCls:$,className:a()(H,r)},B("drop")));else{const e=P.length>0;A=u().createElement("div",{className:a()($,H,{[`${$}-rtl`]:"rtl"===E},i,r),style:{...o,...s},dir:E||"ltr",ref:N},u().createElement(co,{prefixCls:$,items:P,onRemove:I,overflow:v,upload:D,listClassName:a()(M.list,x.list),listStyle:{...R.list,...S.list,...!e&&{display:"none"}},uploadClassName:a()(M.upload,x.upload),uploadStyle:{...R.upload,...S.upload},itemClassName:a()(M.item,x.item),itemStyle:{...R.item,...S.item},imageProps:y}),B("inline",e?{style:{display:"none"}}:{},L),u().createElement(sn,{getDropContainer:d||(()=>N.current),prefixCls:$,className:H},B("drop")))}return z(u().createElement(an.Provider,{value:{disabled:b}},A))}const po=u().forwardRef(mo);po.FileCard=lo;var ho=po,go=i(372);var vo={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"}}]},name:"close",theme:"outlined"},yo=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:vo}))};var bo=c.forwardRef(yo);const xo=c.createContext({}),So=()=>({height:0}),wo=e=>({height:e.scrollHeight});const Co=c.createContext(null);function Eo(e,t){const{className:o,action:i,onClick:s,...l}=e,u=c.useContext(Co),{prefixCls:d,disabled:f}=u,m=l.disabled??f??u[`${i}Disabled`];return c.createElement(r.Button,n()({type:"text"},l,{ref:t,onClick:e=>{m||(u[i]?.(),s?.(e))},className:a()(d,o,{[`${d}-disabled`]:m})}))}var $o=c.forwardRef(Eo),ko={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6a25.95 25.95 0 0025.6 30.4h723c1.5 0 3-.1 4.4-.4a25.88 25.88 0 0021.2-30zM204 390h272V182h72v208h272v104H204V390zm468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z"}}]},name:"clear",theme:"outlined"},Mo=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:ko}))};var Ro=c.forwardRef(Mo);function No(e,t){return c.createElement($o,n()({icon:c.createElement(Ro,null)},e,{action:"onClear",ref:t}))}var Lo=c.forwardRef(No);var zo=(0,c.memo)((e=>{const{className:t}=e;return u().createElement("svg",{color:"currentColor",viewBox:"0 0 1000 1000",xmlns:"http://www.w3.org/2000/svg",className:t},u().createElement("title",null,"Stop Loading"),u().createElement("rect",{fill:"currentColor",height:"250",rx:"24",ry:"24",width:"250",x:"375",y:"375"}),u().createElement("circle",{cx:"500",cy:"500",fill:"none",r:"450",stroke:"currentColor",strokeWidth:"100",opacity:"0.45"}),u().createElement("circle",{cx:"500",cy:"500",fill:"none",r:"450",stroke:"currentColor",strokeWidth:"100",strokeDasharray:"600 9999999"},u().createElement("animateTransform",{attributeName:"transform",dur:"1s",from:"0 500 500",repeatCount:"indefinite",to:"360 500 500",type:"rotate"})))}));function To(e,t){const{prefixCls:r}=c.useContext(Co),{className:o}=e;return c.createElement($o,n()({icon:null,color:"primary",variant:"text",shape:"circle"},e,{className:a()(o,`${r}-loading-button`),action:"onCancel",ref:t}),c.createElement(zo,{className:`${r}-loading-icon`}))}var Oo=c.forwardRef(To),Ho={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"},Po=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:Ho}))};var jo=c.forwardRef(Po);function _o(e,t){return c.createElement($o,n()({icon:c.createElement(jo,null),type:"primary",shape:"circle"},e,{action:"onSend",ref:t}))}var Do=c.forwardRef(_o),Io={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M682 455V311l-76 76v68c-.1 50.7-42 92.1-94 92a95.8 95.8 0 01-52-15l-54 55c29.1 22.4 65.9 36 106 36 93.8 0 170-75.1 170-168z"}},{tag:"path",attrs:{d:"M833 446h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254-63 0-120.7-23-165-61l-54 54a334.01 334.01 0 00179 81v102H326c-13.9 0-24.9 14.3-25 32v36c.1 4.4 2.9 8 6 8h408c3.2 0 6-3.6 6-8v-36c0-17.7-11-32-25-32H547V782c165.3-17.9 294-157.9 294-328 0-4.4-3.6-8-8-8zm13.1-377.7l-43.5-41.9a8 8 0 00-11.2.1l-129 129C634.3 101.2 577 64 511 64c-93.9 0-170 75.3-170 168v224c0 6.7.4 13.3 1.2 19.8l-68 68A252.33 252.33 0 01258 454c-.2-4.4-3.8-8-8-8h-60c-4.4 0-8 3.6-8 8 0 53 12.5 103 34.6 147.4l-137 137a8.03 8.03 0 000 11.3l42.7 42.7c3.1 3.1 8.2 3.1 11.3 0L846.2 79.8l.1-.1c3.1-3.2 3-8.3-.2-11.4zM417 401V232c0-50.6 41.9-92 94-92 46 0 84.1 32.3 92.3 74.7L417 401z"}}]},name:"audio-muted",theme:"outlined"},Ao=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:Io}))};var Bo=c.forwardRef(Ao),Xo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M842 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254S258 594.3 258 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 168.7 126.6 307.9 290 327.6V884H326.7c-13.7 0-24.7 14.3-24.7 32v36c0 4.4 2.8 8 6.2 8h407.6c3.4 0 6.2-3.6 6.2-8v-36c0-17.7-11-32-24.7-32H548V782.1c165.3-18 294-158 294-328.1zM512 624c93.9 0 170-75.2 170-168V232c0-92.8-76.1-168-170-168s-170 75.2-170 168v224c0 92.8 76.1 168 170 168zm-94-392c0-50.6 41.9-92 94-92s94 41.4 94 92v224c0 50.6-41.9 92-94 92s-94-41.4-94-92V232z"}}]},name:"audio",theme:"outlined"},Fo=function(e,t){return c.createElement(Xe,h({},e,{ref:t,icon:Xo}))};var qo=c.forwardRef(Fo);const Vo=140,Wo=250;function Go({className:e}){return u().createElement("svg",{color:"currentColor",viewBox:"0 0 1000 1000",xmlns:"http://www.w3.org/2000/svg",className:e},u().createElement("title",null,"Speech Recording"),Array.from({length:4}).map(((e,t)=>{const n=286.66666666666663*t;return u().createElement("rect",{fill:"currentColor",rx:70,ry:70,height:Wo,width:Vo,x:n,y:375,key:t},u().createElement("animate",{attributeName:"height",values:"250; 500; 250",keyTimes:"0; 0.5; 1",dur:"0.8s",begin:.2*t+"s",repeatCount:"indefinite"}),u().createElement("animate",{attributeName:"y",values:"375; 250; 375",keyTimes:"0; 0.5; 1",dur:"0.8s",begin:.2*t+"s",repeatCount:"indefinite"}))})))}function Uo(e,t){const{speechRecording:r,onSpeechDisabled:o,prefixCls:a}=c.useContext(Co);let i=null;return i=r?c.createElement(Go,{className:`${a}-recording-icon`}):o?c.createElement(Bo,null):c.createElement(qo,null),c.createElement($o,n()({icon:i,color:"primary",variant:"text"},e,{action:"onSpeech",ref:t}))}var Ko=c.forwardRef(Uo);var Zo=e=>{const{componentCls:t,calc:n}=e,r=`${t}-header`;return{[t]:{[r]:{borderBottomWidth:e.lineWidth,borderBottomStyle:"solid",borderBottomColor:e.colorBorder,"&-header":{background:e.colorFillAlter,fontSize:e.fontSize,lineHeight:e.lineHeight,paddingBlock:n(e.paddingSM).sub(e.lineWidthBold).equal(),paddingInlineStart:e.padding,paddingInlineEnd:e.paddingXS,display:"flex",borderRadius:{_skip_check_:!0,value:n(e.borderRadius).mul(2).equal()},borderEndStartRadius:0,borderEndEndRadius:0,[`${r}-title`]:{flex:"auto"}},"&-content":{padding:e.padding},"&-motion":{transition:["height","border"].map((t=>`${t} ${e.motionDurationSlow}`)).join(","),overflow:"hidden","&-enter-start, &-leave-active":{borderBottomColor:"transparent"},"&-hidden":{display:"none"}}}}}};const Yo=e=>{const{componentCls:t,padding:n,paddingSM:r,paddingXS:o,paddingXXS:a,lineWidth:i,lineWidthBold:s,calc:l}=e;return{[t]:{position:"relative",width:"100%",boxSizing:"border-box",boxShadow:`${e.boxShadowTertiary}`,transition:`background ${e.motionDurationSlow}`,borderRadius:{_skip_check_:!0,value:l(e.borderRadius).mul(2).equal()},borderColor:e.colorBorder,borderWidth:0,borderStyle:"solid","&:after":{content:'""',position:"absolute",inset:0,pointerEvents:"none",transition:`border-color ${e.motionDurationSlow}`,borderRadius:{_skip_check_:!0,value:"inherit"},borderStyle:"inherit",borderColor:"inherit",borderWidth:i},"&:focus-within":{boxShadow:`${e.boxShadowSecondary}`,borderColor:e.colorPrimary,"&:after":{borderWidth:s}},"&-disabled":{background:e.colorBgContainerDisabled},[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{display:"flex",gap:o,width:"100%",paddingBlock:r,paddingInlineStart:n,paddingInlineEnd:r,boxSizing:"border-box",alignItems:"flex-end"},[`${t}-prefix`]:{flex:"none"},[`${t}-input`]:{padding:0,borderRadius:0,flex:"auto",alignSelf:"center",minHeight:"auto"},[`${t}-actions-list`]:{flex:"none",display:"flex","&-presets":{gap:e.paddingXS}},[`${t}-actions-btn`]:{"&-disabled":{opacity:.45},"&-loading-button":{padding:0,border:0},"&-loading-icon":{height:e.controlHeight,width:e.controlHeight,verticalAlign:"top"},"&-recording-icon":{height:"1.2em",width:"1.2em",verticalAlign:"top"}},[`${t}-footer`]:{paddingInlineStart:n,paddingInlineEnd:r,paddingBlockEnd:r,paddingBlockStart:a,boxSizing:"border-box"}}}};var Qo=Yt("Sender",(e=>{const{paddingXS:t,calc:n}=e,r=Lt(e,{SenderContentMaxWidth:`calc(100% - ${(0,Ge.unit)(n(t).add(32).equal())})`});return[Yo(r),Zo(r)]}),(()=>({})));let Jo;function ea(e,t){const n=ft(e),[r,o,a]=u().useMemo((()=>"object"==typeof t?[t.recording,t.onRecordingChange,"boolean"==typeof t.recording]:[void 0,void 0,!1]),[t]),[i,s]=u().useState(null);u().useEffect((()=>{if("undefined"!=typeof navigator&&"permissions"in navigator){let e=null;return navigator.permissions.query({name:"microphone"}).then((t=>{s(t.state),t.onchange=function(){s(this.state)},e=t})),()=>{e&&(e.onchange=null)}}}),[]);const l=Jo&&"denied"!==i,c=u().useRef(null),[d,f]=yt(!1,{value:r}),m=u().useRef(!1),p=ft((e=>{e&&!d||(m.current=e,a?o?.(!d):((()=>{if(l&&!c.current){const e=new Jo;e.onstart=()=>{f(!0)},e.onend=()=>{f(!1)},e.onresult=e=>{if(!m.current){const t=e.results?.[0]?.[0]?.transcript;n(t)}m.current=!1},c.current=e}})(),c.current&&(d?(c.current.stop(),o?.(!1)):(c.current.start(),o?.(!0)))))}));return[l,p,d]}Jo||"undefined"==typeof window||(Jo=window.SpeechRecognition||window.webkitSpeechRecognition);const ta={SendButton:Do,ClearButton:Lo,LoadingButton:Oo,SpeechButton:Ko},na=u().forwardRef(((e,t)=>{const{prefixCls:o,styles:i={},classNames:s={},className:d,rootClassName:f,style:h,defaultValue:g,value:v,readOnly:y,submitType:b="enter",onSubmit:x,loading:S,components:w,onCancel:C,onChange:E,actions:$,onKeyPress:k,onKeyDown:M,disabled:R,allowSpeech:N,prefix:L,footer:z,header:T,onPaste:O,onPasteFile:H,autoSize:P={maxRows:8},...j}=e,{direction:_,getPrefixCls:D}=p(),I=D("sender",o),A=u().useRef(null),B=u().useRef(null);!function(e,t){(0,c.useImperativeHandle)(e,(()=>{const e=t(),{nativeElement:n}=e;return new Proxy(n,{get(t,n){return e[n]?e[n]:Reflect.get(t,n)}})}))}(t,(()=>({nativeElement:A.current,focus:B.current?.focus,blur:B.current?.blur})));const X=m("sender"),F=`${I}-input`,[q,V,W]=Qo(I),G=a()(I,X.className,d,f,V,W,{[`${I}-rtl`]:"rtl"===_,[`${I}-disabled`]:R}),U=`${I}-actions-btn`,K=`${I}-actions-list`,[Z,Y]=yt(g||"",{value:v}),Q=(e,t)=>{Y(e),E&&E(e,t)},[J,ee,te]=ea((e=>{Q(`${Z} ${e}`)}),N),ne=function(e,t,n){return(0,go.Z)(e,t)||n}(w,["input"],r.Input.TextArea),re={...(0,l.Z)(j,{attr:!0,aria:!0,data:!0}),ref:B},oe=()=>{Z&&x&&!S&&x(Z)},ae=u().useRef(!1);let ie=u().createElement(r.Flex,{className:`${K}-presets`},N&&u().createElement(Ko,null),S?u().createElement(Oo,null):u().createElement(Do,null));"function"==typeof $?ie=$(ie,{components:ta}):($||!1===$)&&(ie=$);const se={prefixCls:U,onSend:oe,onSendDisabled:!Z,onClear:()=>{Q("")},onClearDisabled:!Z,onCancel:C,onCancelDisabled:!S,onSpeech:()=>ee(!1),onSpeechDisabled:!J,speechRecording:te,disabled:R},le="function"==typeof z?z({components:ta}):z||null;return q(u().createElement("div",{ref:A,className:G,style:{...X.style,...h}},T&&u().createElement(xo.Provider,{value:{prefixCls:I}},T),u().createElement(Co.Provider,{value:se},u().createElement("div",{className:`${I}-content`,onMouseDown:e=>{e.target!==A.current?.querySelector(`.${F}`)&&e.preventDefault(),B.current?.focus()}},L&&u().createElement("div",{className:a()(`${I}-prefix`,X.classNames.prefix,s.prefix),style:{...X.styles.prefix,...i.prefix}},L),u().createElement(ne,n()({},re,{disabled:R,style:{...X.styles.input,...i.input},className:a()(F,X.classNames.input,s.input),autoSize:P,value:Z,onChange:e=>{Q(e.target.value,e),ee(!0)},onPressEnter:e=>{const t="Enter"===e.key&&!ae.current;switch(b){case"enter":t&&!e.shiftKey&&(e.preventDefault(),oe());break;case"shiftEnter":t&&e.shiftKey&&(e.preventDefault(),oe())}k?.(e)},onCompositionStart:()=>{ae.current=!0},onCompositionEnd:()=>{ae.current=!1},onKeyDown:M,onPaste:e=>{const t=e.clipboardData?.files;t?.length&&H&&(H(t[0],t),e.preventDefault()),O?.(e)},variant:"borderless",readOnly:y})),ie&&u().createElement("div",{className:a()(K,X.classNames.actions,s.actions),style:{...X.styles.actions,...i.actions}},ie)),le&&u().createElement("div",{className:a()(`${I}-footer`,X.classNames.footer,s.footer),style:{...X.styles.footer,...i.footer}},le))))})),ra=na;ra.Header=function(e){const{title:t,onOpenChange:n,open:o,children:i,className:s,style:l,classNames:u={},styles:d={},closable:f,forceRender:m}=e,{prefixCls:p}=c.useContext(xo),h=`${p}-header`;return c.createElement(yr,{motionEnter:!0,motionLeave:!0,motionName:`${h}-motion`,leavedClassName:`${h}-motion-hidden`,onEnterStart:So,onEnterActive:wo,onLeaveStart:wo,onLeaveActive:So,visible:o,forceRender:m},(({className:e,style:m})=>c.createElement("div",{className:a()(h,e,s),style:{...m,...l}},(!1!==f||t)&&c.createElement("div",{className:a()(`${h}-header`,u.header),style:{...d.header}},c.createElement("div",{className:`${h}-title`},t),!1!==f&&c.createElement("div",{className:`${h}-close`},c.createElement(r.Button,{type:"text",icon:c.createElement(bo,null),size:"small",onClick:()=>{n?.(!o)}}))),i&&c.createElement("div",{className:a()(`${h}-content`,u.content),style:{...d.content}},i))))};var oa=ra,aa=i(208);function ia(e){return"string"==typeof e}var sa=(e,t,n,r)=>{const o=c.useRef(""),[a,i]=c.useState(1),s=t&&ia(e);(0,aa.default)((()=>{!s&&ia(e)?i(e.length):ia(e)&&ia(o.current)&&0!==e.indexOf(o.current)&&i(1),o.current=e}),[e]),c.useEffect((()=>{if(s&&a<e.length){const e=setTimeout((()=>{i((e=>e+n))}),r);return()=>{clearTimeout(e)}}}),[a,t,e]);return[s?e.slice(0,a):e,s&&a<e.length]};var la=function(e){return c.useMemo((()=>{if(!e)return[!1,0,0,null];let t={step:1,interval:50,suffix:null};return"object"==typeof e&&(t={...t,...e}),[!0,t.step,t.interval,t.suffix]}),[e])};var ca=({prefixCls:e})=>u().createElement("span",{className:`${e}-dot`},u().createElement("i",{className:`${e}-dot-item`,key:"item-1"}),u().createElement("i",{className:`${e}-dot-item`,key:"item-2"}),u().createElement("i",{className:`${e}-dot-item`,key:"item-3"}));const ua=e=>{const{componentCls:t,paddingSM:n,padding:r}=e;return{[t]:{[`${t}-content`]:{"&-filled,&-outlined,&-shadow":{padding:`${(0,Ge.unit)(n)} ${(0,Ge.unit)(r)}`,borderRadius:e.borderRadiusLG},"&-filled":{backgroundColor:e.colorFillContent},"&-outlined":{border:`1px solid ${e.colorBorderSecondary}`},"&-shadow":{boxShadow:e.boxShadowTertiary}}}}},da=e=>{const{componentCls:t,fontSize:n,lineHeight:r,paddingSM:o,padding:a,calc:i}=e,s=`${t}-content`;return{[t]:{[s]:{"&-round":{borderRadius:{_skip_check_:!0,value:i(n).mul(r).div(2).add(o).equal()},paddingInline:i(a).mul(1.25).equal()}},[`&-start ${s}-corner`]:{borderStartStartRadius:e.borderRadiusXS},[`&-end ${s}-corner`]:{borderStartEndRadius:e.borderRadiusXS}}}};var fa=e=>{const{componentCls:t,padding:n}=e;return{[`${t}-list`]:{display:"flex",flexDirection:"column",gap:n,overflowY:"auto","&::-webkit-scrollbar":{width:8,backgroundColor:"transparent"},"&::-webkit-scrollbar-thumb":{backgroundColor:e.colorTextTertiary,borderRadius:e.borderRadiusSM},"&":{scrollbarWidth:"thin",scrollbarColor:`${e.colorTextTertiary} transparent`}}}};const ma=new Ge.Keyframes("loadingMove",{"0%":{transform:"translateY(0)"},"10%":{transform:"translateY(4px)"},"20%":{transform:"translateY(0)"},"30%":{transform:"translateY(-4px)"},"40%":{transform:"translateY(0)"}}),pa=new Ge.Keyframes("cursorBlink",{"0%":{opacity:1},"50%":{opacity:0},"100%":{opacity:1}}),ha=e=>{const{componentCls:t,fontSize:n,lineHeight:r,paddingSM:o,colorText:a,calc:i}=e;return{[t]:{display:"flex",columnGap:o,[`&${t}-end`]:{justifyContent:"end",flexDirection:"row-reverse",[`& ${t}-content-wrapper`]:{alignItems:"flex-end"}},[`&${t}-rtl`]:{direction:"rtl"},[`&${t}-typing ${t}-content:last-child::after`]:{content:'"|"',fontWeight:900,userSelect:"none",opacity:1,marginInlineStart:"0.1em",animationName:pa,animationDuration:"0.8s",animationIterationCount:"infinite",animationTimingFunction:"linear"},[`& ${t}-avatar`]:{display:"inline-flex",justifyContent:"center",alignSelf:"flex-start"},[`& ${t}-header, & ${t}-footer`]:{fontSize:n,lineHeight:r,color:e.colorText},[`& ${t}-header`]:{marginBottom:e.paddingXXS},[`& ${t}-footer`]:{marginTop:o},[`& ${t}-content-wrapper`]:{flex:"auto",display:"flex",flexDirection:"column",alignItems:"flex-start",minWidth:0,maxWidth:"100%"},[`& ${t}-content`]:{position:"relative",boxSizing:"border-box",minWidth:0,maxWidth:"100%",color:a,fontSize:e.fontSize,lineHeight:e.lineHeight,minHeight:i(o).mul(2).add(i(r).mul(n)).equal(),wordBreak:"break-word",[`& ${t}-dot`]:{position:"relative",height:"100%",display:"flex",alignItems:"center",columnGap:e.marginXS,padding:`0 ${(0,Ge.unit)(e.paddingXXS)}`,"&-item":{backgroundColor:e.colorPrimary,borderRadius:"100%",width:4,height:4,animationName:ma,animationDuration:"2s",animationIterationCount:"infinite",animationTimingFunction:"linear","&:nth-child(1)":{animationDelay:"0s"},"&:nth-child(2)":{animationDelay:"0.2s"},"&:nth-child(3)":{animationDelay:"0.4s"}}}}}}};var ga=Yt("Bubble",(e=>{const t=Lt(e,{});return[ha(t),fa(t),ua(t),da(t)]}),(()=>({})));const va=u().createContext({}),ya=(e,t)=>{const{prefixCls:o,className:i,rootClassName:s,style:l,classNames:c={},styles:d={},avatar:f,placement:h="start",loading:g=!1,loadingRender:v,typing:y,content:b="",messageRender:x,variant:S="filled",shape:w,onTypingComplete:C,header:E,footer:$,_key:k,...M}=e,{onUpdate:R}=u().useContext(va),N=u().useRef(null);u().useImperativeHandle(t,(()=>({nativeElement:N.current})));const{direction:L,getPrefixCls:z}=p(),T=z("bubble",o),O=m("bubble"),[H,P,j,_]=la(y),[D,I]=sa(b,H,P,j);u().useEffect((()=>{R?.()}),[D]);const A=u().useRef(!1);u().useEffect((()=>{I||g?A.current=!1:A.current||(A.current=!0,C?.())}),[I,g]);const[B,X,F]=ga(T),q=a()(T,s,O.className,i,X,F,`${T}-${h}`,{[`${T}-rtl`]:"rtl"===L,[`${T}-typing`]:I&&!g&&!x&&!_}),V=u().useMemo((()=>u().isValidElement(f)?f:u().createElement(r.Avatar,f)),[f]),W=u().useMemo((()=>x?x(D):D),[D,x]),G=e=>"function"==typeof e?e(D,{key:k}):e;let U;U=g?v?v():u().createElement(ca,{prefixCls:T}):u().createElement(u().Fragment,null,W,I&&_);let K=u().createElement("div",{style:{...O.styles.content,...d.content},className:a()(`${T}-content`,`${T}-content-${S}`,w&&`${T}-content-${w}`,O.classNames.content,c.content)},U);return(E||$)&&(K=u().createElement("div",{className:`${T}-content-wrapper`},E&&u().createElement("div",{className:a()(`${T}-header`,O.classNames.header,c.header),style:{...O.styles.header,...d.header}},G(E)),K,$&&u().createElement("div",{className:a()(`${T}-footer`,O.classNames.footer,c.footer),style:{...O.styles.footer,...d.footer}},G($)))),B(u().createElement("div",n()({style:{...O.style,...l},className:q},M,{ref:N}),f&&u().createElement("div",{style:{...O.styles.avatar,...d.avatar},className:a()(`${T}-avatar`,O.classNames.avatar,c.avatar)},V),K))};var ba=u().forwardRef(ya);const xa=({_key:e,...t},r)=>c.createElement(ba,n()({},t,{_key:e,ref:t=>{t?r.current[e]=t:delete r.current?.[e]}})),Sa=c.memo(c.forwardRef(xa)),wa=(e,t)=>{const{prefixCls:r,rootClassName:o,className:i,items:s,autoScroll:u=!0,roles:d,onScroll:f,...m}=e,h=(0,l.Z)(m,{attr:!0,aria:!0}),g=c.useRef(null),v=c.useRef({}),{getPrefixCls:y}=p(),b=y("bubble",r),x=`${b}-list`,[S,w,C]=ga(b),[E,$]=c.useState(!1);c.useEffect((()=>($(!0),()=>{$(!1)})),[]);const k=function(e,t){const n=c.useCallback(((e,n)=>"function"==typeof t?t(e,n):t&&t[e.role]||{}),[t]);return c.useMemo((()=>(e||[]).map(((e,t)=>{const r=e.key??`preset_${t}`;return{...n(e,t),...e,key:r}}))),[e,n])}(s,d),[M,R]=c.useState(!0),[N,L]=c.useState(0);c.useEffect((()=>{u&&g.current&&M&&g.current.scrollTo({top:g.current.scrollHeight})}),[N]),c.useEffect((()=>{if(u){const e=k[k.length-2]?.key,t=v.current[e];if(t){const{nativeElement:e}=t,{top:n,bottom:r}=e.getBoundingClientRect(),{top:o,bottom:a}=g.current.getBoundingClientRect();n<a&&r>o&&(L((e=>e+1)),R(!0))}}}),[k.length]),c.useImperativeHandle(t,(()=>({nativeElement:g.current,scrollTo:({key:e,offset:t,behavior:n="smooth",block:r})=>{if("number"==typeof t)g.current.scrollTo({top:t,behavior:n});else if(void 0!==e){const t=v.current[e];if(t){const o=k.findIndex((t=>t.key===e));R(o===k.length-1),t.nativeElement.scrollIntoView({behavior:n,block:r})}}}})));const z=ft((()=>{u&&L((e=>e+1))})),T=c.useMemo((()=>({onUpdate:z})),[]);return S(c.createElement(va.Provider,{value:T},c.createElement("div",n()({},h,{className:a()(x,o,i,w,C,{[`${x}-reach-end`]:M}),ref:g,onScroll:e=>{const t=e.target;R(t.scrollHeight-Math.abs(t.scrollTop)-t.clientHeight<=1),f?.(e)}}),k.map((({key:e,...t})=>c.createElement(Sa,n()({},t,{key:e,_key:e,ref:v,typing:!!E&&t.typing})))))))};var Ca=c.forwardRef(wa);ba.List=Ca;var Ea=ba;const $a=u().createContext(null);var ka=({children:e})=>{const{prefixCls:t}=u().useContext($a);return u().createElement("div",{className:a()(`${t}-group-title`)},e&&u().createElement(r.Typography.Text,null,e))};const Ma=e=>{e.stopPropagation()};var Ra=e=>{const{prefixCls:t,info:o,className:i,direction:s,onClick:c,active:d,menu:f,...m}=e,p=(0,l.Z)(m,{aria:!0,data:!0,attr:!0}),{disabled:h}=o,g=a()(i,`${t}-item`,{[`${t}-item-active`]:d&&!h},{[`${t}-item-disabled`]:h}),{trigger:v,...y}=f||{},b=y?.getPopupContainer;return u().createElement("li",n()({title:"object"==typeof o.label?void 0:`${o.label}`},p,{className:g,onClick:()=>{!h&&c&&c(o)}}),o.icon&&u().createElement("div",{className:`${t}-icon`},o.icon),u().createElement(r.Typography.Text,{className:`${t}-label`},o.label),!h&&f&&u().createElement("div",{onClick:Ma},u().createElement(r.Dropdown,{menu:y,placement:"rtl"===s?"bottomLeft":"bottomRight",trigger:["click"],disabled:h,getPopupContainer:b},(e=>{const n=u().createElement(qe,{onClick:Ma,className:`${t}-menu-icon`});return v?"function"==typeof v?v(e,{originNode:n}):v:n})(o))))},Na=i(905);const La="__ungrouped";var za=(e,t=[])=>{const[n,r,o]=u().useMemo((()=>{if(!e)return[!1,void 0,void 0];let t={sort:void 0,title:void 0};return"object"==typeof e&&(t={...t,...e}),[!0,t.sort,t.title]}),[e]);return u().useMemo((()=>{if(!n){return[[{name:La,data:t,title:void 0}],n]}const e=t.reduce(((e,t)=>{const n=t.group||La;return e[n]||(e[n]=[]),e[n].push(t),e}),{});return[(r?Object.keys(e).sort(r):Object.keys(e)).map((t=>({name:t===La?void 0:t,title:o,data:e[t]}))),n]}),[t,e])};var Ta=Yt("Conversations",(e=>(e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexDirection:"column",gap:e.paddingXXS,overflowY:"auto",padding:e.paddingSM,margin:0,listStyle:"none","ul, ol":{margin:0,padding:0,listStyle:"none"},[`&${t}-rtl`]:{direction:"rtl"},[`& ${t}-list`]:{display:"flex",gap:e.paddingXXS,flexDirection:"column",[`& ${t}-item`]:{paddingInlineStart:e.paddingXL},[`& ${t}-label`]:{color:e.colorTextDescription,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},[`& ${t}-item`]:{display:"flex",height:e.controlHeightLG,minHeight:e.controlHeightLG,gap:e.paddingXS,padding:`0 ${(0,Ge.unit)(e.paddingXS)}`,alignItems:"center",borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,"&:hover":{backgroundColor:e.colorBgTextHover},"&-active":{backgroundColor:e.colorBgTextHover,[`& ${t}-label, ${t}-menu-icon`]:{color:e.colorText}},"&-disabled":{cursor:"not-allowed",[`& ${t}-label`]:{color:e.colorTextDisabled}},"&:hover, &-active":{[`& ${t}-menu-icon`]:{opacity:.6}},[`${t}-menu-icon:hover`]:{opacity:1}},[`& ${t}-label`]:{flex:1,color:e.colorText,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},[`& ${t}-menu-icon`]:{opacity:0,fontSize:e.fontSizeXL},[`& ${t}-group-title`]:{display:"flex",alignItems:"center",height:e.controlHeightLG,minHeight:e.controlHeightLG,padding:`0 ${(0,Ge.unit)(e.paddingXS)}`}}}})(Lt(e,{}))),(()=>({})));var Oa=e=>{const{prefixCls:t,rootClassName:r,items:o,activeKey:i,defaultActiveKey:s,onActiveChange:c,menu:d,styles:f={},classNames:h={},groupable:g,className:v,style:y,...b}=e,x=(0,l.Z)(b,{attr:!0,aria:!0,data:!0}),[S,w]=(0,Na.Z)(s,{value:i}),[C,E]=za(g,o),{getPrefixCls:$,direction:k}=p(),M=$("conversations",t),R=m("conversations"),[N,L,z]=Ta(M),T=a()(M,R.className,v,r,L,z,{[`${M}-rtl`]:"rtl"===k}),O=e=>{w(e.key),c&&c(e.key)};return N(u().createElement("ul",n()({},x,{style:{...R.style,...y},className:T}),C.map(((e,t)=>{const r=e.data.map(((e,t)=>{const{label:r,disabled:o,icon:i,...s}=e;return u().createElement(Ra,n()({},s,{key:e.key||`key-${t}`,info:e,prefixCls:M,direction:k,className:a()(h.item,R.classNames.item,e.className),style:{...R.styles.item,...f.item,...e.style},menu:"function"==typeof d?d(e):d,active:S===e.key,onClick:O}))}));return E?u().createElement("li",{key:e.name||`key-${t}`},u().createElement($a.Provider,{value:{prefixCls:M}},e.title?.(e.name,{components:{GroupTitle:ka}})||u().createElement(ka,{key:e.name},e.name)),u().createElement("ul",{className:`${M}-list`},r)):r}))))};const Ha=e=>{const{componentCls:t}=e;return{[t]:{"&, & *":{boxSizing:"border-box"},maxWidth:"100%",[`&${t}-rtl`]:{direction:"rtl"},[`& ${t}-title`]:{marginBlockStart:0,fontWeight:"normal",color:e.colorTextTertiary},[`& ${t}-list`]:{display:"flex",gap:e.paddingSM,overflowX:"auto",scrollbarWidth:"none","-ms-overflow-style":"none","&::-webkit-scrollbar":{display:"none"},listStyle:"none",paddingInlineStart:0,marginBlock:0,alignItems:"stretch","&-wrap":{flexWrap:"wrap"},"&-vertical":{flexDirection:"column",alignItems:"flex-start"}},[`${t}-item`]:{flex:"none",display:"flex",gap:e.paddingXS,height:"auto",paddingBlock:e.paddingSM,paddingInline:e.padding,alignItems:"flex-start",justifyContent:"flex-start",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,transition:["border","background"].map((t=>`${t} ${e.motionDurationSlow}`)).join(","),border:`${(0,Ge.unit)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,[`&:not(${t}-item-has-nest)`]:{"&:hover":{cursor:"pointer",background:e.colorFillTertiary},"&:active":{background:e.colorFill}},[`${t}-content`]:{flex:"auto",minWidth:0,display:"flex",gap:e.paddingXXS,flexDirection:"column",alignItems:"flex-start"},[`${t}-icon, ${t}-label, ${t}-desc`]:{margin:0,padding:0,fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start",whiteSpace:"normal"},[`${t}-label`]:{color:e.colorTextHeading,fontWeight:500},[`${t}-label + ${t}-desc`]:{color:e.colorTextTertiary},[`&${t}-item-disabled`]:{pointerEvents:"none",background:e.colorBgContainerDisabled,[`${t}-label, ${t}-desc`]:{color:e.colorTextTertiary}}}}}},Pa=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-item-has-nest`]:{[`> ${t}-content`]:{[`> ${t}-label`]:{fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}}},[`&${t}-nested`]:{marginTop:e.paddingXS,alignSelf:"stretch",[`${t}-list`]:{alignItems:"stretch"},[`${t}-item`]:{border:0,background:e.colorFillQuaternary}}}}};var ja=Yt("Prompts",(e=>{const t=Lt(e,{});return[Ha(t),Pa(t)]}),(()=>({})));const _a=e=>{const{prefixCls:t,title:o,className:i,items:s,onItemClick:l,vertical:c,wrap:d,rootClassName:f,styles:h={},classNames:g={},style:v,...y}=e,{getPrefixCls:b,direction:x}=p(),S=b("prompts",t),w=m("prompts"),[C,E,$]=ja(S),k=a()(S,w.className,i,f,E,$,{[`${S}-rtl`]:"rtl"===x}),M=a()(`${S}-list`,w.classNames.list,g.list,{[`${S}-list-wrap`]:d},{[`${S}-list-vertical`]:c});return C(u().createElement("div",n()({},y,{className:k,style:{...v,...w.style}}),o&&u().createElement(r.Typography.Title,{level:5,className:a()(`${S}-title`,w.classNames.title,g.title),style:{...w.styles.title,...h.title}},o),u().createElement("div",{className:M,style:{...w.styles.list,...h.list}},s?.map(((e,t)=>{const n=e.children&&e.children.length>0;return u().createElement("div",{key:e.key||`key_${t}`,style:{...w.styles.item,...h.item},className:a()(`${S}-item`,w.classNames.item,g.item,{[`${S}-item-disabled`]:e.disabled,[`${S}-item-has-nest`]:n}),onClick:()=>{!n&&l&&l({data:e})}},e.icon&&u().createElement("div",{className:`${S}-icon`},e.icon),u().createElement("div",{className:a()(`${S}-content`,w.classNames.itemContent,g.itemContent),style:{...w.styles.itemContent,...h.itemContent}},e.label&&u().createElement("h6",{className:`${S}-label`},e.label),e.description&&u().createElement("p",{className:`${S}-desc`},e.description),n&&u().createElement(_a,{className:`${S}-nested`,items:e.children,vertical:!0,onItemClick:l,classNames:{list:g.subList,item:g.subItem},styles:{list:h.subList,item:h.subItem}})))})))))};var Da=_a;const Ia=()=>({height:0,opacity:0}),Aa=e=>{const{scrollHeight:t}=e;return{height:t,opacity:1}},Ba=e=>({height:e?e.offsetHeight:0}),Xa=(e,t)=>!0===t?.deadline||"height"===t.propertyName;var Fa=(e="ant")=>({motionName:`${e}-motion-collapse`,onAppearStart:Ia,onEnterStart:Ia,onAppearActive:Aa,onEnterActive:Aa,onLeaveStart:Ba,onLeaveActive:Ia,onAppearEnd:Xa,onEnterEnd:Xa,onLeaveEnd:Xa,motionDeadline:500});var qa=(e,t,n)=>{const r="boolean"==typeof e||void 0===e?.expandedKeys,[o,a,i]=u().useMemo((()=>{let t={expandedKeys:[],onExpand:()=>{}};return e?("object"==typeof e&&(t={...t,...e}),[!0,t.expandedKeys,t.onExpand]):[!1,t.expandedKeys,t.onExpand]}),[e]),[s,l]=(0,Na.Z)(a,{value:r?void 0:a,onChange:i}),c=u().useMemo((()=>o?{...Fa(n),motionAppear:!1,leavedClassName:`${t}-content-hidden`}:{}),[n,t,o]);return[o,s,o?e=>{l((t=>{const n=r?t:a,o=n.includes(e)?n.filter((t=>t!==e)):[...n,e];return i?.(o),o}))}:void 0,c]};var Va=e=>({[e.componentCls]:{[`${e.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},\n        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}},[`${e.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},\n        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}}});let Wa=function(e){return e.PENDING="pending",e.SUCCESS="success",e.ERROR="error",e}({});const Ga=u().createContext(null);var Ua=e=>{const{info:t={},nextStatus:o,onClick:i,...s}=e,c=(0,l.Z)(s,{attr:!0,aria:!0,data:!0}),{prefixCls:d,collapseMotion:f,enableCollapse:m,expandedKeys:p,direction:h,classNames:g={},styles:v={}}=u().useContext(Ga),y=u().useId(),{key:b=y,icon:x,title:S,extra:w,content:C,footer:E,status:$,description:k}=t,M=`${d}-item`,R=p?.includes(b);return u().createElement("div",n()({},c,{className:a()(M,{[`${M}-${$}${o?`-${o}`:""}`]:$},e.className),style:e.style}),u().createElement("div",{className:a()(`${M}-header`,g.itemHeader),style:v.itemHeader,onClick:()=>i?.(b)},u().createElement(r.Avatar,{icon:x,className:`${M}-icon`}),u().createElement("div",{className:a()(`${M}-header-box`,{[`${M}-collapsible`]:m&&C})},u().createElement(r.Typography.Text,{strong:!0,ellipsis:{tooltip:{placement:"rtl"===h?"topRight":"topLeft",title:S}},className:`${M}-title`},m&&C&&("rtl"===h?u().createElement(mn,{className:`${M}-collapse-icon`,rotate:R?-90:0}):u().createElement(gn,{className:`${M}-collapse-icon`,rotate:R?90:0})),S),k&&u().createElement(r.Typography.Text,{className:`${M}-desc`,ellipsis:{tooltip:{placement:"rtl"===h?"topRight":"topLeft",title:k}},type:"secondary"},k)),w&&u().createElement("div",{className:`${M}-extra`},w)),C&&u().createElement(yr,n()({},f,{visible:!m||R}),(({className:e,style:t},n)=>u().createElement("div",{className:a()(`${M}-content`,e),ref:n,style:t},u().createElement("div",{className:a()(`${M}-content-box`,g.itemContent),style:v.itemContent},C)))),E&&u().createElement("div",{className:a()(`${M}-footer`,g.itemFooter),style:v.itemFooter},E))};const Ka=e=>{const{componentCls:t}=e,n=`${t}-item`,r={[Wa.PENDING]:e.colorPrimaryText,[Wa.SUCCESS]:e.colorSuccessText,[Wa.ERROR]:e.colorErrorText},o=Object.keys(r);return o.reduce(((e,t)=>{const a=r[t];return o.forEach((o=>{const i=t===o?{}:{backgroundColor:"none !important",backgroundImage:`linear-gradient(${a}, ${r[o]})`};e[`& ${n}-${t}-${o}`]={[`& ${n}-icon, & > *::before`]:{backgroundColor:`${a} !important`},"& > :last-child::before":i}})),e}),{})},Za=e=>{const{calc:t,componentCls:n}=e,r=`${n}-item`,o={content:'""',width:t(e.lineWidth).mul(2).equal(),display:"block",position:"absolute",insetInlineEnd:"none",backgroundColor:e.colorTextPlaceholder};return{"& > :last-child > :last-child":{"&::before":{display:"none !important"},[`&${r}-footer`]:{"&::before":{display:"block !important",bottom:0}}},[`& > ${r}`]:{[`& ${r}-header, & ${r}-content, & ${r}-footer`]:{position:"relative","&::before":{bottom:t(e.itemGap).mul(-1).equal()}},[`& ${r}-header, & ${r}-content`]:{marginInlineStart:t(e.itemSize).mul(-1).equal(),"&::before":{...o,insetInlineStart:t(e.itemSize).div(2).sub(e.lineWidth).equal()}},[`& ${r}-header::before`]:{top:e.itemSize,bottom:t(e.itemGap).mul(-2).equal()},[`& ${r}-content::before`]:{top:"100%"},[`& ${r}-footer::before`]:{...o,top:0,insetInlineStart:t(e.itemSize).div(-2).sub(e.lineWidth).equal()}}}},Ya=e=>{const{componentCls:t}=e,n=`${t}-item`;return{[n]:{display:"flex",flexDirection:"column",[`& ${n}-collapsible`]:{cursor:"pointer"},[`& ${n}-header`]:{display:"flex",marginBottom:e.itemGap,gap:e.itemGap,alignItems:"flex-start",[`& ${n}-icon`]:{height:e.itemSize,width:e.itemSize,fontSize:e.itemFontSize},[`& ${n}-extra`]:{height:e.itemSize,maxHeight:e.itemSize},[`& ${n}-header-box`]:{flex:1,display:"flex",flexDirection:"column",overflow:"hidden",[`& ${n}-title`]:{height:e.itemSize,lineHeight:`${(0,Ge.unit)(e.itemSize)}`,maxHeight:e.itemSize,fontSize:e.itemFontSize,[`& ${n}-collapse-icon`]:{marginInlineEnd:e.marginXS}},[`& ${n}-desc`]:{fontSize:e.itemFontSize}}},[`& ${n}-content`]:{[`& ${n}-content-hidden`]:{display:"none"},[`& ${n}-content-box`]:{padding:e.itemGap,display:"inline-block",maxWidth:`calc(100% - ${e.itemSize})`,borderRadius:e.borderRadiusLG,backgroundColor:e.colorBgContainer,border:`${(0,Ge.unit)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}},[`& ${n}-footer`]:{marginTop:e.itemGap,display:"inline-flex"}}}},Qa=(e,t="middle")=>{const{componentCls:n}=e,r={large:{itemSize:e.itemSizeLG,itemGap:e.itemGapLG,itemFontSize:e.itemFontSizeLG},middle:{itemSize:e.itemSize,itemGap:e.itemGap,itemFontSize:e.itemFontSize},small:{itemSize:e.itemSizeSM,itemGap:e.itemGapSM,itemFontSize:e.itemFontSizeSM}}[t];return{[`&${n}-${t}`]:{paddingInlineStart:r.itemSize,gap:r.itemGap,...Ya({...e,...r}),...Za({...e,...r})}}},Ja=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexDirection:"column",...Ka(e),...Qa(e),...Qa(e,"large"),...Qa(e,"small"),[`&${t}-rtl`]:{direction:"rtl"}}}};var ei=Yt("ThoughtChain",(e=>{const t=Lt(e,{itemFontSizeSM:e.fontSizeSM,itemSizeSM:e.calc(e.controlHeightXS).add(e.controlHeightSM).div(2).equal(),itemGapSM:e.marginSM,itemFontSize:e.fontSize,itemSize:e.calc(e.controlHeightSM).add(e.controlHeight).div(2).equal(),itemGap:e.margin,itemFontSizeLG:e.fontSizeLG,itemSizeLG:e.calc(e.controlHeight).add(e.controlHeightLG).div(2).equal(),itemGapLG:e.marginLG});return[Ja(t),Va(t)]}));var ti=e=>{const{prefixCls:t,rootClassName:r,className:o,items:i,collapsible:s,styles:c={},style:d,classNames:f={},size:h="middle",...g}=e,v=(0,l.Z)(g,{attr:!0,aria:!0,data:!0}),{getPrefixCls:y,direction:b}=p(),x=y(),S=y("thought-chain",t),w=m("thoughtChain"),[C,E,$,k]=qa(s,S,x),[M,R,N]=ei(S),L=a()(o,r,S,w.className,R,N,{[`${S}-rtl`]:"rtl"===b},`${S}-${h}`);return M(u().createElement("div",n()({},v,{className:L,style:{...w.style,...d}}),u().createElement(Ga.Provider,{value:{prefixCls:S,enableCollapse:C,collapseMotion:k,expandedKeys:E,direction:b,classNames:{itemHeader:a()(w.classNames.itemHeader,f.itemHeader),itemContent:a()(w.classNames.itemContent,f.itemContent),itemFooter:a()(w.classNames.itemFooter,f.itemFooter)},styles:{itemHeader:{...w.styles.itemHeader,...c.itemHeader},itemContent:{...w.styles.itemContent,...c.itemContent},itemFooter:{...w.styles.itemFooter,...c.itemFooter}}}},i?.map(((e,t)=>u().createElement(Ua,{key:e.key||`key_${t}`,className:a()(w.classNames.item,f.item),style:{...w.styles.item,...c.item},info:{...e,icon:e.icon||t+1},onClick:$,nextStatus:i[t+1]?.status||e.status}))))))};var ni=Yt("Suggestion",(e=>(e=>{const{componentCls:t,antCls:n}=e;return{[t]:{[`${n}-cascader-menus ${n}-cascader-menu`]:{height:"auto"},[`${t}-item`]:{"&-icon":{marginInlineEnd:e.paddingXXS},"&-extra":{marginInlineStart:e.padding}},[`&${t}-block`]:{[`${t}-item-extra`]:{marginInlineStart:"auto"}}}}})(Lt(e,{}))),(()=>({})));function ri(e,t,n,r,o){const[a,i]=u().useState([]),s=(t,n=a)=>{let r=e;for(let e=0;e<t-1;e+=1){const t=n[e],o=r.find((e=>e.value===t));if(!o)break;r=o.children||[]}return r},l=e=>{const t=a.length||1,n=s(t),r=n.findIndex((e=>e.value===a[t-1])),o=n.length,l=n[(r+e+o)%o];i([...a.slice(0,t-1),l.value])},c=()=>{a.length>1&&i(a.slice(0,a.length-1))},d=()=>{const e=s(a.length+1);e.length&&i([...a,e[0].value])},f=ft((e=>{var i;if(t)switch(e.key){case"ArrowDown":l(1),e.preventDefault();break;case"ArrowUp":l(-1),e.preventDefault();break;case"ArrowRight":n?c():d(),e.preventDefault();break;case"ArrowLeft":n?d():c(),e.preventDefault();break;case"Enter":s(a.length+1).length||r((i=a).map(((e,t)=>s(t+1,i).find((t=>t.value===e))?.value))),e.preventDefault();break;case"Escape":o(),e.preventDefault()}}));return u().useEffect((()=>{t&&i([e[0].value])}),[t]),[a,f]}const oi=r.version.split(".").map(Number),ai=oi[0]>5||5===oi[0]&&oi[1]>=25;var ii=function(e){const{prefixCls:t,className:o,rootClassName:i,style:s,children:l,open:d,onOpenChange:f,items:h,onSelect:g,block:v}=e,{direction:y,getPrefixCls:b}=p(),x=b("suggestion",t),S=`${x}-item`,w="rtl"===y,C=m("suggestion"),[E,$,k]=ni(x),[M,R]=yt(!1,{value:d}),[N,L]=(0,c.useState)(),z=e=>{R(e),f?.(e)},T=ft((e=>{!1===e?z(!1):(L(e),z(!0))})),O=()=>{z(!1)},H=u().useMemo((()=>"function"==typeof h?h(N):h),[h,N]),P=e=>{g&&g(e[e.length-1]),z(!1)},[j,_]=ri(H,M,w,P,O),D=l?.({onTrigger:T,onKeyDown:_}),I=e=>{e||O()},A={};return ai?A.onOpenChange=I:A.onDropdownVisibleChange=I,E(u().createElement(r.Cascader,n()({options:H,open:M,value:j,placement:w?"topRight":"topLeft"},A,{optionRender:e=>u().createElement(r.Flex,{className:S},e.icon&&u().createElement("div",{className:`${S}-icon`},e.icon),e.label,e.extra&&u().createElement("div",{className:`${S}-extra`},e.extra)),rootClassName:a()(i,x,$,k,{[`${x}-block`]:v}),onChange:P,dropdownMatchSelectWidth:v}),u().createElement("div",{className:a()(x,C.className,i,o,`${x}-wrapper`,$,k),style:{...C.style,...s}},D)))};const si=e=>{const{componentCls:t,calc:n}=e,r=n(e.fontSizeHeading3).mul(e.lineHeightHeading3).equal(),o=n(e.fontSize).mul(e.lineHeight).equal();return{[t]:{gap:e.padding,[`${t}-icon`]:{height:n(r).add(o).add(e.paddingXXS).equal(),display:"flex",img:{height:"100%"}},[`${t}-content-wrapper`]:{gap:e.paddingXS,flex:"auto",minWidth:0,[`${t}-title-wrapper`]:{gap:e.paddingXS},[`${t}-title`]:{margin:0},[`${t}-extra`]:{marginInlineStart:"auto"}}}}},li=e=>{const{componentCls:t}=e;return{[t]:{"&-filled":{paddingInline:e.padding,paddingBlock:e.paddingSM,background:e.colorFillContent,borderRadius:e.borderRadiusLG},"&-borderless":{[`${t}-title`]:{fontSize:e.fontSizeHeading3,lineHeight:e.lineHeightHeading3}}}}};var ci=Yt("Welcome",(e=>{const t=Lt(e,{});return[si(t),li(t)]}),(()=>({})));function ui(e,t){const{prefixCls:n,rootClassName:o,className:i,style:s,variant:l="filled",classNames:c={},styles:d={},icon:f,title:h,description:g,extra:v}=e,{direction:y,getPrefixCls:b}=p(),x=b("welcome",n),S=m("welcome"),[w,C,E]=ci(x),$=u().useMemo((()=>{if(!f)return null;let e=f;return"string"==typeof f&&f.startsWith("http")&&(e=u().createElement("img",{src:f,alt:"icon"})),u().createElement("div",{className:a()(`${x}-icon`,S.classNames.icon,c.icon),style:d.icon},e)}),[f]),k=u().useMemo((()=>h?u().createElement(r.Typography.Title,{level:4,className:a()(`${x}-title`,S.classNames.title,c.title),style:d.title},h):null),[h]),M=u().useMemo((()=>v?u().createElement("div",{className:a()(`${x}-extra`,S.classNames.extra,c.extra),style:d.extra},v):null),[v]);return w(u().createElement(r.Flex,{ref:t,className:a()(x,S.className,i,o,C,E,`${x}-${l}`,{[`${x}-rtl`]:"rtl"===y}),style:s},$,u().createElement(r.Flex,{vertical:!0,className:`${x}-content-wrapper`},v?u().createElement(r.Flex,{align:"flex-start",className:`${x}-title-wrapper`},k,M):k,g&&u().createElement(r.Typography.Text,{className:a()(`${x}-description`,S.classNames.description,c.description),style:d.description},g))))}var di=u().forwardRef(ui);var fi=e=>{const{attachments:t,bubble:o,conversations:a,prompts:i,sender:s,suggestion:l,thoughtChain:c,welcome:f,theme:m,...h}=e,{theme:g}=p(),v=u().useMemo((()=>({attachments:t,bubble:o,conversations:a,prompts:i,sender:s,suggestion:l,thoughtChain:c,welcome:f})),[t,o,a,i,s,l,c,f]),y=u().useMemo((()=>({...g,...m})),[g,m]);return u().createElement(d.Provider,{value:v},u().createElement(r.ConfigProvider,n()({},h,{theme:y})))};function mi(e){const{defaultMessages:t,agent:n,requestFallback:r,requestPlaceholder:o,parser:a,transformMessage:i,transformStream:s,resolveAbortController:l}=e,c=u().useRef(0),[d,f,m]=function(e){const[,t]=u().useState(0),n=u().useRef("function"==typeof e?e():e),r=u().useCallback((e=>{n.current="function"==typeof e?e(n.current):e,t((e=>e+1))}),[]),o=u().useCallback((()=>n.current),[]);return[n.current,r,o]}((()=>(t||[]).map(((e,t)=>({id:`default_${t}`,status:"local",...e}))))),p=(e,t)=>{const n={id:`msg_${c.current}`,message:e,status:t};return c.current+=1,n},h=u().useMemo((()=>{const e=[];return d.forEach((t=>{const n=a?a(t.message):t.message,r=(o=n,Array.isArray(o)?o:[o]);var o;r.forEach(((n,o)=>{let a=t.id;r.length>1&&(a=`${a}_${o}`),e.push({id:a,message:n,status:t.status})}))})),e}),[d]),g=e=>e.filter((e=>"loading"!==e.status&&"error"!==e.status)).map((e=>e.message)),v=()=>g(m()),y=e=>{const{chunk:t,chunks:n,originMessage:r}=e;if("function"==typeof i)return i(e);if(t)return t;if(Array.isArray(n)){return r||(n?.length>0?n?.[n?.length-1]:void 0)}return n};return{onRequest:ft((e=>{if(!n)throw new Error("The agent parameter is required when using the onRequest method in an agent generated by useXAgent.");let t,a=null,i={};if(e&&"object"==typeof e&&"message"in e){const{message:n,...r}=e;t=n,i=r}else t=e;f((e=>{let n=[...e,p(t,"local")];if(o){let e;e="function"==typeof o?o(t,{messages:g(n)}):o;const r=p(e,"loading");a=r.id,n=[...n,r]}return n}));let c=null;const u=(e,t,n)=>{let r=m().find((e=>e.id===c));if(r)f((r=>r.map((r=>{if(r.id===c){const o=y({originMessage:r.message,chunk:t,chunks:n,status:e});return{...r,message:o,status:e}}return r}))));else{const o=y({chunk:t,status:e,chunks:n});r=p(o,e),f((e=>[...e.filter((e=>e.id!==a)),r])),c=r.id}return r};n.request({message:t,messages:v(),...i},{onUpdate:e=>{u("loading",e,[])},onSuccess:e=>{u("success",void 0,e)},onError:async e=>{if(r){let n;n="function"==typeof r?await r(t,{error:e,messages:v()}):r,f((e=>[...e.filter((e=>e.id!==a&&e.id!==c)),p(n,"error")]))}else f((e=>e.filter((e=>e.id!==a&&e.id!==c))))},onStream:e=>{l?.(e)}},s)})),messages:d,parsedMessages:h,setMessages:f}}const pi=e=>""!==(e??"").trim();var hi=function(e){const{readableStream:t,transformStream:n}=e;if(!(t instanceof ReadableStream))throw new Error("The options.readableStream must be an instance of ReadableStream.");const r=new TextDecoderStream,o=n?t.pipeThrough(r).pipeThrough(n):t.pipeThrough(r).pipeThrough(function(){let e="";return new TransformStream({transform(t,n){e+=t;const r=e.split("\n\n");r.slice(0,-1).forEach((e=>{pi(e)&&n.enqueue(e)})),e=r[r.length-1]},flush(t){pi(e)&&t.enqueue(e)}})}()).pipeThrough(new TransformStream({transform(e,t){const n=e.split("\n").reduce(((e,t)=>{const n=t.indexOf(":");if(-1===n)throw new Error('The key-value separator ":" is not found in the sse line chunk!');const r=t.slice(0,n);if(!pi(r))return e;const o=t.slice(n+1);return{...e,[r]:o}}),{});0!==Object.keys(n).length&&t.enqueue(n)}}));return o[Symbol.asyncIterator]=async function*(){const e=this.getReader();for(;;){const{done:t,value:n}=await e.read();if(t)break;n&&(yield n)}},o};var gi=async(e,t={})=>{const{fetch:n=globalThis.fetch,middlewares:r={},...o}=t;if("function"!=typeof n)throw new Error("The options.fetch must be a typeof fetch function!");let a=[e,o];if("function"==typeof r.onRequest){a=await r.onRequest(...a)}let i=await n(...a);if("function"==typeof r.onResponse){const e=await r.onResponse(i);if(!(e instanceof Response))throw new Error("The options.onResponse must return a Response instance!");i=e}if(!i.ok)throw new Error(`Fetch failed with status ${i.status}`);if(!i.body)throw new Error("The response body is empty.");return i};class vi{baseURL;model;defaultHeaders;customOptions;constructor(e){const{baseURL:t,model:n,dangerouslyApiKey:r,...o}=e;this.baseURL=e.baseURL,this.model=e.model,this.defaultHeaders={"Content-Type":"application/json",...e.dangerouslyApiKey&&{Authorization:e.dangerouslyApiKey}},this.customOptions=o}static init(e){if(!e.baseURL||"string"!=typeof e.baseURL)throw new Error("The baseURL is not valid!");return new vi(e)}create=async(e,t,n)=>{const r=new AbortController,o={method:"POST",body:JSON.stringify({model:this.model,...e}),headers:this.defaultHeaders,signal:r.signal};t?.onStream?.(r);try{const e=await gi(this.baseURL,{fetch:this.customOptions.fetch,...o});if(n)return void await this.customResponseHandler(e,t,n);const r=e.headers.get("content-type")||"";switch(r.split(";")[0].trim()){case"text/event-stream":await this.sseResponseHandler(e,t);break;case"application/json":await this.jsonResponseHandler(e,t);break;default:throw new Error(`The response content-type: ${r} is not support!`)}}catch(e){const n=e instanceof Error?e:new Error("Unknown error!");throw t?.onError?.(n),n}};customResponseHandler=async(e,t,n)=>{const r=[];for await(const o of hi({readableStream:e.body,transformStream:n}))r.push(o),t?.onUpdate?.(o);t?.onSuccess?.(r)};sseResponseHandler=async(e,t)=>{const n=[],r=hi({readableStream:e.body});for await(const e of r)n.push(e),t?.onUpdate?.(e);t?.onSuccess?.(n)};jsonResponseHandler=async(e,t)=>{const n=await e.json();t?.onUpdate?.(n),t?.onSuccess?.([n])}}var yi=vi.init;let bi=0;class xi{config;requestingMap={};constructor(e){this.config=e}finishRequest(e){delete this.requestingMap[e]}request=(e,t,n)=>{const{request:r}=this.config,{onUpdate:o,onSuccess:a,onError:i,onStream:s}=t,l=bi;bi+=1,this.requestingMap[l]=!0,r?.(e,{onStream:e=>{this.requestingMap[l]&&s?.(e)},onUpdate:e=>{this.requestingMap[l]&&o(e)},onSuccess:e=>{this.requestingMap[l]&&(a(e),this.finishRequest(l))},onError:e=>{this.requestingMap[l]&&(i(e),this.finishRequest(l))}},n)};isRequesting(){return Object.keys(this.requestingMap).length>0}}function Si(e){const{request:t,...n}=e;return u().useMemo((()=>[new xi({request:t||yi({baseURL:n.baseURL,model:n.model,dangerouslyApiKey:n.dangerouslyApiKey}).create,...n})]),[e?.baseURL,e?.dangerouslyApiKey,e?.model])}}(),s}()}));
//# sourceMappingURL=antdx.min.js.map