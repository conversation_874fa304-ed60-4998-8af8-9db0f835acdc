/// <reference types="react" />
import type { FullToken } from '../../theme/cssinjs-utils';
export interface ComponentToken {
}
export interface ThoughtChainToken extends FullToken<'ThoughtChain'> {
    /**
     * @desc default size for item font size
     */
    itemFontSize: number;
    /**
     * @desc default size for item
     */
    itemSize: number;
    /**
     * @desc gap between items
     */
    itemGap: number;
    /**
     * @desc large size for item font size
     */
    itemFontSizeLG: number;
    /**
     * @desc large size for item
     */
    itemSizeLG: number;
    /**
     * @desc large gap between items
     */
    itemGapLG: number;
    /**
     * @desc small size for item font size
     */
    itemFontSizeSM: number;
    /**
     * @desc small size for item
     */
    itemSizeSM: number;
    /**
     * @desc small gap between items
     */
    itemGapSM: number;
}
declare const _default: (prefixCls: string, rootCls?: string | undefined) => readonly [(node: import("react").ReactElement<unknown, string | import("react").JSXElementConstructor<any>>) => import("react").ReactElement<unknown, string | import("react").JSXElementConstructor<any>>, string, string];
export default _default;
