import _extends from "@babel/runtime/helpers/esm/extends";
import { ArrowUpOutlined } from '@ant-design/icons';
import * as React from 'react';
import ActionButton from "./ActionButton";
function SendButton(props, ref) {
  return /*#__PURE__*/React.createElement(ActionButton, _extends({
    icon: /*#__PURE__*/React.createElement(ArrowUpOutlined, null),
    type: "primary",
    shape: "circle"
  }, props, {
    action: "onSend",
    ref: ref
  }));
}
export default /*#__PURE__*/React.forwardRef(SendButton);