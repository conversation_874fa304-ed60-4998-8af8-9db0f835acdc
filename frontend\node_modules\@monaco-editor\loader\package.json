{"name": "@monaco-editor/loader", "version": "1.5.0", "description": "the library aims to setup monaco editor into your browser", "main": "lib/cjs/index.js", "module": "lib/es/index.js", "unpkg": "lib/umd/monaco-loader.min.js", "jsdelivr": "lib/umd/monaco-loader.min.js", "types": "lib/types.d.ts", "repository": "https://github.com/suren-atoyan/monaco-loader.git", "homepage": "https://github.com/suren-atoyan/monaco-loader.git", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"test": "jest", "test-watch": "npm run build && jest --watch", "coverage": "jest --collect-coverage", "lint": "npx eslint src", "prepublishOnly": "npm test && npm run lint && npm run build", "build": "rollup -c && cp ./src/types.d.ts ./lib/"}, "keywords": ["monaco", "editor", "loader", "monaco-editor", "monaco editor"], "devDependencies": {"@babel/core": "^7.11.0", "@babel/preset-env": "^7.11.0", "@rollup/plugin-babel": "^5.2.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.1", "@rollup/plugin-replace": "^2.3.4", "babel-jest": "^26.2.2", "babel-loader": "^8.1.0", "eslint": "^7.6.0", "husky": "^4.2.5", "jest": "^26.2.2", "rollup": "^2.35.1", "rollup-plugin-terser": "^7.0.2"}, "dependencies": {"state-local": "^1.0.6"}}