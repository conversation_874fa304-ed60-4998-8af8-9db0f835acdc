export declare const defaultPrefixCls = "ant";
declare function useXProviderContext(): {
    theme: import("antd").ThemeConfig | undefined;
    getPrefixCls: (suffixCls?: string | undefined, customizePrefixCls?: string | undefined) => string;
    direction: import("antd/es/config-provider").DirectionType;
    csp: import("antd/es/config-provider").CSPConfig | undefined;
    iconPrefixCls: string;
};
export default useXProviderContext;
