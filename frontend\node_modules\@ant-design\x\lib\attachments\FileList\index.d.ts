import { type ImageProps, type UploadProps } from 'antd';
import React from 'react';
import type { Attachment } from '..';
export interface FileListProps {
    prefixCls: string;
    items: Attachment[];
    onRemove: (item: Attachment) => void;
    overflow?: 'scrollX' | 'scrollY' | 'wrap';
    upload: UploadProps;
    imageProps?: ImageProps;
    listClassName?: string;
    listStyle?: React.CSSProperties;
    itemClassName?: string;
    itemStyle?: React.CSSProperties;
    uploadClassName?: string;
    uploadStyle?: React.CSSProperties;
}
export default function FileList(props: FileListProps): React.JSX.Element;
