import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

class TemplateService:
    def __init__(self):
        self.templates_db: Dict[str, Dict[str, Any]] = {}
        self._init_default_templates()
    
    def _init_default_templates(self):
        """初始化默认模板"""
        default_templates = [
            {
                "name": "Web功能测试模板",
                "description": "适用于Web应用功能测试的标准模板",
                "template_type": "functional",
                "content": {
                    "sections": [
                        {
                            "name": "用户界面测试",
                            "description": "测试用户界面元素和交互",
                            "test_points": [
                                "页面布局和样式",
                                "表单验证",
                                "按钮和链接功能",
                                "响应式设计"
                            ]
                        },
                        {
                            "name": "业务流程测试",
                            "description": "测试核心业务流程",
                            "test_points": [
                                "用户注册流程",
                                "登录认证流程",
                                "数据提交流程",
                                "支付流程"
                            ]
                        },
                        {
                            "name": "数据验证测试",
                            "description": "测试数据输入和验证",
                            "test_points": [
                                "必填字段验证",
                                "数据格式验证",
                                "数据长度限制",
                                "特殊字符处理"
                            ]
                        }
                    ],
                    "test_case_structure": {
                        "title": "测试用例标题",
                        "description": "测试用例描述",
                        "preconditions": ["前置条件1", "前置条件2"],
                        "steps": [
                            {
                                "step_number": 1,
                                "action": "执行操作",
                                "expected_result": "预期结果"
                            }
                        ],
                        "expected_result": "最终预期结果",
                        "priority": "High/Medium/Low",
                        "category": "功能测试",
                        "tags": ["标签1", "标签2"]
                    }
                },
                "is_public": True
            },
            {
                "name": "API接口测试模板",
                "description": "适用于API接口测试的标准模板",
                "template_type": "api",
                "content": {
                    "sections": [
                        {
                            "name": "接口基础测试",
                            "description": "测试接口基本功能",
                            "test_points": [
                                "请求方法验证",
                                "请求参数验证",
                                "响应状态码验证",
                                "响应数据格式验证"
                            ]
                        },
                        {
                            "name": "接口异常测试",
                            "description": "测试接口异常处理",
                            "test_points": [
                                "无效参数测试",
                                "缺失参数测试",
                                "权限验证测试",
                                "超时处理测试"
                            ]
                        },
                        {
                            "name": "接口性能测试",
                            "description": "测试接口性能指标",
                            "test_points": [
                                "响应时间测试",
                                "并发请求测试",
                                "大数据量测试",
                                "压力测试"
                            ]
                        }
                    ],
                    "test_case_structure": {
                        "title": "API测试用例标题",
                        "description": "API测试用例描述",
                        "api_info": {
                            "method": "GET/POST/PUT/DELETE",
                            "url": "接口地址",
                            "headers": {},
                            "parameters": {}
                        },
                        "preconditions": ["前置条件"],
                        "steps": [
                            {
                                "step_number": 1,
                                "action": "发送请求",
                                "expected_result": "返回正确响应"
                            }
                        ],
                        "expected_result": "接口返回预期数据",
                        "priority": "High",
                        "category": "接口测试",
                        "tags": ["API", "自动化"]
                    }
                },
                "is_public": True
            },
            {
                "name": "移动应用测试模板",
                "description": "适用于移动应用测试的标准模板",
                "template_type": "mobile",
                "content": {
                    "sections": [
                        {
                            "name": "设备兼容性测试",
                            "description": "测试不同设备的兼容性",
                            "test_points": [
                                "不同屏幕尺寸适配",
                                "不同操作系统版本",
                                "不同设备性能",
                                "横竖屏切换"
                            ]
                        },
                        {
                            "name": "功能测试",
                            "description": "测试应用核心功能",
                            "test_points": [
                                "应用启动和关闭",
                                "页面导航",
                                "数据输入和提交",
                                "推送通知"
                            ]
                        },
                        {
                            "name": "性能测试",
                            "description": "测试应用性能表现",
                            "test_points": [
                                "启动时间",
                                "内存使用",
                                "电池消耗",
                                "网络使用"
                            ]
                        }
                    ],
                    "test_case_structure": {
                        "title": "移动应用测试用例",
                        "description": "移动应用测试描述",
                        "device_info": {
                            "platform": "iOS/Android",
                            "version": "系统版本",
                            "device_model": "设备型号"
                        },
                        "preconditions": ["安装应用", "网络连接"],
                        "steps": [
                            {
                                "step_number": 1,
                                "action": "启动应用",
                                "expected_result": "应用正常启动"
                            }
                        ],
                        "expected_result": "功能正常运行",
                        "priority": "High",
                        "category": "移动测试",
                        "tags": ["移动", "兼容性"]
                    }
                },
                "is_public": True
            },
            {
                "name": "安全测试模板",
                "description": "适用于应用安全测试的标准模板",
                "template_type": "security",
                "content": {
                    "sections": [
                        {
                            "name": "身份认证测试",
                            "description": "测试身份认证机制",
                            "test_points": [
                                "登录验证",
                                "密码策略",
                                "会话管理",
                                "多因素认证"
                            ]
                        },
                        {
                            "name": "输入验证测试",
                            "description": "测试输入数据验证",
                            "test_points": [
                                "SQL注入防护",
                                "XSS攻击防护",
                                "CSRF攻击防护",
                                "文件上传安全"
                            ]
                        },
                        {
                            "name": "数据安全测试",
                            "description": "测试数据保护机制",
                            "test_points": [
                                "数据加密",
                                "传输安全",
                                "敏感信息保护",
                                "数据备份安全"
                            ]
                        }
                    ],
                    "test_case_structure": {
                        "title": "安全测试用例",
                        "description": "安全测试描述",
                        "security_type": "认证/授权/数据保护",
                        "preconditions": ["测试环境准备"],
                        "steps": [
                            {
                                "step_number": 1,
                                "action": "执行安全测试",
                                "expected_result": "安全机制有效"
                            }
                        ],
                        "expected_result": "无安全漏洞",
                        "priority": "High",
                        "category": "安全测试",
                        "tags": ["安全", "漏洞"]
                    }
                },
                "is_public": True
            }
        ]
        
        for template_data in default_templates:
            template_id = str(uuid.uuid4())
            template = {
                "template_id": template_id,
                "name": template_data["name"],
                "description": template_data["description"],
                "template_type": template_data["template_type"],
                "content": template_data["content"],
                "is_public": template_data["is_public"],
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "usage_count": 0
            }
            self.templates_db[template_id] = template
    
    def create_template(self, request) -> Dict[str, Any]:
        """创建新模板"""
        template_id = str(uuid.uuid4())
        
        template = {
            "template_id": template_id,
            "name": request.name,
            "description": request.description,
            "template_type": request.template_type,
            "content": request.content,
            "is_public": request.is_public,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "usage_count": 0
        }
        
        self.templates_db[template_id] = template
        return template
    
    def get_all_templates(self) -> List[Dict[str, Any]]:
        """获取所有模板"""
        return list(self.templates_db.values())
    
    def get_public_templates(self) -> List[Dict[str, Any]]:
        """获取公开模板"""
        return [template for template in self.templates_db.values() if template["is_public"]]
    
    def get_templates_by_type(self, template_type: str) -> List[Dict[str, Any]]:
        """根据类型获取模板"""
        return [template for template in self.templates_db.values() 
                if template["template_type"] == template_type]
    
    def get_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取单个模板"""
        return self.templates_db.get(template_id)
    
    def update_template(self, template_id: str, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新模板"""
        if template_id not in self.templates_db:
            raise Exception("模板不存在")
        
        template = self.templates_db[template_id]
        template.update(template_data)
        template["updated_at"] = datetime.now().isoformat()
        
        return template
    
    def delete_template(self, template_id: str) -> bool:
        """删除模板"""
        if template_id not in self.templates_db:
            return False
        
        del self.templates_db[template_id]
        return True
    
    def increment_usage(self, template_id: str):
        """增加模板使用次数"""
        if template_id in self.templates_db:
            self.templates_db[template_id]["usage_count"] += 1
    
    def get_template_stats(self) -> Dict[str, Any]:
        """获取模板统计信息"""
        total_templates = len(self.templates_db)
        public_templates = len([t for t in self.templates_db.values() if t["is_public"]])
        
        type_stats = {}
        for template in self.templates_db.values():
            template_type = template["template_type"]
            if template_type not in type_stats:
                type_stats[template_type] = 0
            type_stats[template_type] += 1
        
        return {
            "total_templates": total_templates,
            "public_templates": public_templates,
            "private_templates": total_templates - public_templates,
            "type_distribution": type_stats
        }
