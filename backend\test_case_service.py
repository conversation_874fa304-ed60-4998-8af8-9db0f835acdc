import asyncio
import json
import uuid
import base64
from datetime import datetime
from typing import AsyncGenerator, Dict, List, Any, Optional
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient
from dotenv import load_dotenv
import os
from image_analysis_service import ImageAnalysisService

# 加载环境变量
load_dotenv()

class TestCaseService:
    def __init__(self):
        self.test_cases_db: Dict[str, Dict[str, Any]] = {}
        self.project_test_cases: Dict[str, List[str]] = {}
        self.model_client = self._create_model_client()
        self.image_analysis_service = ImageAnalysisService()
        self.image_analysis_service = ImageAnalysisService()
        
    def _create_model_client(self):
        """创建模型客户端"""
        return OpenAIChatCompletionClient(
            model="gpt-4o-mini",  # 使用支持视觉的模型
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
        )

    def _analyze_page_images(self, images: List[str], ui_descriptions: List[str] = None) -> str:
        """分析页面截图，提取UI元素和交互信息"""
        if not images:
            return ""

        try:
            # 使用图片分析服务
            analysis_result = self.image_analysis_service.analyze_page_screenshots(images, ui_descriptions)

            # 提取摘要信息用于测试用例生成
            summary = self.image_analysis_service.extract_ui_elements_summary(analysis_result)

            return summary

        except Exception as e:
            print(f"图片分析失败: {str(e)}")
            return f"检测到 {len(images)} 张页面截图，包含多个UI元素和交互组件，需要进行全面的功能测试。"
    
    def _create_test_case_agent(self, generation_type: str) -> AssistantAgent:
        """创建测试用例生成代理"""
        
        system_messages = {
            "functional": """
你是一名拥有超过10年经验的资深软件测试架构师，精通各种测试方法论（如：等价类划分、边界值分析、因果图、场景法等）。

你的任务是为给定的功能需求，设计一份专业、全面、且易于执行的高质量测试用例。

**测试用例设计原则：**
1. 覆盖正常流程、异常流程、边界条件
2. 使用等价类划分和边界值分析
3. 考虑用户体验和系统性能
4. 确保测试步骤清晰、可执行
5. 包含明确的预期结果

**输出格式要求：**
请以JSON格式输出测试用例，包含以下字段：
- title: 测试用例标题
- description: 测试用例描述
- preconditions: 前置条件列表
- steps: 测试步骤（包含step_number, action, expected_result）
- expected_result: 最终预期结果
- priority: 优先级（High/Medium/Low）
- category: 测试分类
- tags: 标签列表
""",
            "performance": """
你是一名性能测试专家，专注于系统性能、负载、压力测试用例设计。

请为给定的功能需求设计性能测试用例，重点关注：
1. 响应时间测试
2. 并发用户测试
3. 系统资源使用率测试
4. 数据库性能测试
5. 网络性能测试

输出格式与功能测试用例相同，但重点关注性能指标。
""",
            "security": """
你是一名安全测试专家，专注于应用安全、数据安全测试用例设计。

请为给定的功能需求设计安全测试用例，重点关注：
1. 输入验证和SQL注入
2. 身份认证和授权
3. 会话管理
4. 数据加密和传输安全
5. XSS和CSRF攻击防护

输出格式与功能测试用例相同，但重点关注安全漏洞。
""",
            "api": """
你是一名API测试专家，专注于接口测试用例设计。

请为给定的API需求设计测试用例，重点关注：
1. 请求参数验证
2. 响应数据格式验证
3. HTTP状态码验证
4. 接口性能测试
5. 异常处理测试

输出格式与功能测试用例相同，但重点关注API接口特性。
"""
        }
        
        system_message = system_messages.get(generation_type, system_messages["functional"])
        
        return AssistantAgent(
            "test_case_generator",
            model_client=self.model_client,
            system_message=system_message
        )
    
    async def generate_test_cases_stream(self, request) -> AsyncGenerator[Dict[str, Any], None]:
        """流式生成测试用例"""
        try:
            agent = self._create_test_case_agent(request.generation_type)
            
            # 构建提示词
            prompt = self._build_generation_prompt(request)
            
            # 发送分析开始事件
            yield {
                "type": "analysis",
                "content": "正在分析需求文档...",
                "source": "需求分析"
            }
            
            await asyncio.sleep(0.5)  # 模拟分析时间
            
            # 发送用例设计事件
            yield {
                "type": "design",
                "content": "正在设计测试用例结构...",
                "source": "用例设计"
            }
            
            await asyncio.sleep(0.5)
            
            # 发送生成事件
            yield {
                "type": "generation",
                "content": "正在生成测试用例...",
                "source": "用例生成"
            }
            
            # 模拟AI生成过程
            response = await agent.agenerate_reply([TextMessage(content=prompt, source="user")])
            
            if hasattr(response, 'content'):
                content = response.content
            else:
                content = str(response)
            
            # 解析生成的测试用例
            test_cases = self._parse_generated_test_cases(content, request.project_id)
            
            # 发送生成结果
            yield {
                "type": "result",
                "content": f"成功生成 {len(test_cases)} 个测试用例",
                "source": "生成结果",
                "data": test_cases
            }
            
        except Exception as e:
            yield {
                "type": "error",
                "content": f"生成失败: {str(e)}",
                "source": "错误"
            }
    
    async def generate_test_cases(self, request) -> List[Dict[str, Any]]:
        """非流式生成测试用例"""
        try:
            agent = self._create_test_case_agent(request.generation_type)
            prompt = self._build_generation_prompt(request)
            
            response = await agent.agenerate_reply([TextMessage(content=prompt, source="user")])
            
            if hasattr(response, 'content'):
                content = response.content
            else:
                content = str(response)
            
            test_cases = self._parse_generated_test_cases(content, request.project_id)
            return test_cases
            
        except Exception as e:
            raise Exception(f"生成测试用例失败: {str(e)}")
    
    def _build_generation_prompt(self, request) -> str:
        """构建生成提示词"""
        prompt = f"""
请为以下需求生成测试用例：

**需求描述：**
{request.requirement_text}

**生成要求：**
- 测试类型：{request.generation_type}
- 包含负面测试用例：{'是' if request.include_negative_cases else '否'}
- 包含边界值测试：{'是' if request.include_boundary_cases else '否'}
- 包含性能测试：{'是' if request.include_performance_cases else '否'}
"""

        # 添加页面截图分析
        if hasattr(request, 'page_images') and request.page_images:
            ui_descriptions = getattr(request, 'ui_descriptions', None)
            image_analysis = self._analyze_page_images(request.page_images, ui_descriptions)
            prompt += f"""

**页面截图分析：**
{image_analysis}

请基于页面截图中的UI元素，生成针对性的测试用例，确保覆盖所有可见的交互元素。
"""

        # 添加UI描述
        if hasattr(request, 'ui_descriptions') and request.ui_descriptions:
            ui_desc = "\n".join([f"- {desc}" for desc in request.ui_descriptions])
            prompt += f"""

**UI元素描述：**
{ui_desc}

请结合UI元素描述，确保测试用例覆盖所有描述的界面元素和交互场景。
"""

        prompt += """

请生成完整的测试用例，确保覆盖所有重要场景。特别注意：
1. 基于页面截图和UI描述生成具体的操作步骤
2. 包含对UI元素的具体定位和操作
3. 验证页面元素的显示和交互行为
4. 考虑不同设备和浏览器的兼容性测试
"""
        return prompt
    
    def _parse_generated_test_cases(self, content: str, project_id: str) -> List[Dict[str, Any]]:
        """解析生成的测试用例内容"""
        test_cases = []
        
        try:
            # 尝试解析JSON格式
            if content.strip().startswith('['):
                parsed_cases = json.loads(content)
                if isinstance(parsed_cases, list):
                    for case in parsed_cases:
                        test_case = self._create_test_case_from_dict(case, project_id)
                        test_cases.append(test_case)
            else:
                # 如果不是JSON格式，创建一个基本的测试用例
                test_case = self._create_basic_test_case(content, project_id)
                test_cases.append(test_case)
                
        except json.JSONDecodeError:
            # JSON解析失败，创建基本测试用例
            test_case = self._create_basic_test_case(content, project_id)
            test_cases.append(test_case)
        
        return test_cases
    
    def _create_test_case_from_dict(self, case_dict: Dict[str, Any], project_id: str) -> Dict[str, Any]:
        """从字典创建测试用例"""
        test_case_id = str(uuid.uuid4())
        
        test_case = {
            "test_case_id": test_case_id,
            "project_id": project_id,
            "title": case_dict.get("title", "未命名测试用例"),
            "description": case_dict.get("description", ""),
            "preconditions": case_dict.get("preconditions", []),
            "steps": case_dict.get("steps", []),
            "expected_result": case_dict.get("expected_result", ""),
            "priority": case_dict.get("priority", "Medium"),
            "category": case_dict.get("category", "功能测试"),
            "tags": case_dict.get("tags", []),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "status": "active"
        }
        
        # 保存到数据库
        self.test_cases_db[test_case_id] = test_case
        
        # 添加到项目测试用例列表
        if project_id not in self.project_test_cases:
            self.project_test_cases[project_id] = []
        self.project_test_cases[project_id].append(test_case_id)
        
        return test_case
    
    def _create_basic_test_case(self, content: str, project_id: str) -> Dict[str, Any]:
        """创建基本测试用例"""
        test_case_id = str(uuid.uuid4())
        
        test_case = {
            "test_case_id": test_case_id,
            "project_id": project_id,
            "title": "AI生成的测试用例",
            "description": content[:200] + "..." if len(content) > 200 else content,
            "preconditions": [],
            "steps": [{"step_number": 1, "action": "执行测试", "expected_result": "符合预期"}],
            "expected_result": "测试通过",
            "priority": "Medium",
            "category": "功能测试",
            "tags": ["AI生成"],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "status": "active",
            "raw_content": content
        }
        
        # 保存到数据库
        self.test_cases_db[test_case_id] = test_case
        
        # 添加到项目测试用例列表
        if project_id not in self.project_test_cases:
            self.project_test_cases[project_id] = []
        self.project_test_cases[project_id].append(test_case_id)
        
        return test_case
    
    def get_project_test_cases(self, project_id: str) -> List[Dict[str, Any]]:
        """获取项目的所有测试用例"""
        if project_id not in self.project_test_cases:
            return []
        
        test_cases = []
        for test_case_id in self.project_test_cases[project_id]:
            if test_case_id in self.test_cases_db:
                test_cases.append(self.test_cases_db[test_case_id])
        
        return test_cases
    
    def get_test_case(self, test_case_id: str) -> Optional[Dict[str, Any]]:
        """获取单个测试用例"""
        return self.test_cases_db.get(test_case_id)
    
    def update_test_case(self, test_case_id: str, test_case_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新测试用例"""
        if test_case_id not in self.test_cases_db:
            raise Exception("测试用例不存在")
        
        test_case = self.test_cases_db[test_case_id]
        test_case.update(test_case_data)
        test_case["updated_at"] = datetime.now().isoformat()
        
        return test_case
    
    def delete_test_case(self, test_case_id: str) -> bool:
        """删除测试用例"""
        if test_case_id not in self.test_cases_db:
            return False
        
        test_case = self.test_cases_db[test_case_id]
        project_id = test_case["project_id"]
        
        # 从数据库删除
        del self.test_cases_db[test_case_id]
        
        # 从项目测试用例列表删除
        if project_id in self.project_test_cases:
            if test_case_id in self.project_test_cases[project_id]:
                self.project_test_cases[project_id].remove(test_case_id)
        
        return True
