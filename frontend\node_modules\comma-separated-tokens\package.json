{"name": "comma-separated-tokens", "version": "1.0.8", "description": "Parse and stringify comma-separated tokens", "license": "MIT", "keywords": ["dom", "html", "comma", "separated", "tokens", "parse", "stringify"], "repository": "wooorm/comma-separated-tokens", "bugs": "https://github.com/wooorm/comma-separated-tokens/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["index.js"], "devDependencies": {"browserify": "^16.0.0", "nyc": "^15.0.0", "prettier": "^1.0.0", "remark-cli": "^7.0.0", "remark-preset-wooorm": "^6.0.0", "tape": "^4.0.0", "tinyify": "^2.0.0", "xo": "^0.25.0"}, "scripts": {"format": "remark . -qfo && prettier --write \"**/*.js\" && xo --fix", "build-bundle": "browserify . -s commaSeparatedTokens -o comma-separated-tokens.js", "build-mangle": "browserify . -s commaSeparatedTokens -p tinyify -o comma-separated-tokens.min.js", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test.js", "test": "npm run format && npm run build && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "ignores": ["comma-separated-tokens.js"]}, "remarkConfig": {"plugins": ["preset-wooorm"]}}