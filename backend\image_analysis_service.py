import base64
import json
import os
from typing import List, Dict, Any, Optional
import openai
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class ImageAnalysisService:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
        )
    
    def analyze_page_screenshots(self, images: List[str], ui_descriptions: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        分析页面截图，提取UI元素和测试点
        
        Args:
            images: base64编码的图片列表
            ui_descriptions: UI元素描述列表
            
        Returns:
            分析结果字典，包含UI元素、交互流程、测试建议等
        """
        if not images:
            return {"ui_elements": [], "test_scenarios": [], "analysis": ""}
        
        try:
            # 构建分析提示
            analysis_prompt = self._build_analysis_prompt(ui_descriptions)
            
            # 准备消息内容
            messages = [
                {
                    "role": "system",
                    "content": analysis_prompt
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请分析这些页面截图，识别UI元素和测试场景："
                        }
                    ]
                }
            ]
            
            # 添加图片到消息中
            for i, image_data in enumerate(images[:5]):  # 限制最多5张图片
                # 确保是base64格式
                if image_data.startswith('data:image'):
                    # 提取base64部分
                    image_base64 = image_data.split(',')[1]
                else:
                    image_base64 = image_data
                
                messages[1]["content"].append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{image_base64}",
                        "detail": "high"
                    }
                })
            
            # 调用OpenAI API
            response = self.client.chat.completions.create(
                model="gpt-4o",  # 使用支持视觉的模型
                messages=messages,
                max_tokens=2000,
                temperature=0.3
            )
            
            analysis_text = response.choices[0].message.content
            
            # 解析分析结果
            return self._parse_analysis_result(analysis_text)
            
        except Exception as e:
            print(f"图片分析失败: {str(e)}")
            return {
                "ui_elements": [],
                "test_scenarios": [],
                "analysis": f"图片分析失败: {str(e)}",
                "error": True
            }
    
    def _build_analysis_prompt(self, ui_descriptions: Optional[List[str]] = None) -> str:
        """构建图片分析提示词"""
        prompt = """
你是一名专业的UI/UX测试专家，请分析提供的页面截图，识别以下内容：

1. **UI元素识别**：
   - 按钮、链接、输入框、下拉菜单等交互元素
   - 导航菜单、面包屑、标签页等导航元素
   - 表格、列表、卡片等数据展示元素
   - 弹窗、提示框、加载状态等反馈元素

2. **页面布局分析**：
   - 页面结构和层次关系
   - 响应式设计特点
   - 关键功能区域划分

3. **用户交互流程**：
   - 主要的用户操作路径
   - 表单提交流程
   - 数据查询和筛选流程

4. **测试场景建议**：
   - 功能测试点
   - 界面兼容性测试
   - 用户体验测试
   - 异常场景测试

请以JSON格式返回分析结果，包含以下字段：
{
    "ui_elements": [
        {
            "type": "button|input|link|menu|...",
            "name": "元素名称",
            "location": "元素位置描述",
            "function": "功能描述",
            "test_points": ["测试点1", "测试点2"]
        }
    ],
    "page_layout": {
        "structure": "页面结构描述",
        "key_areas": ["区域1", "区域2"],
        "responsive_features": ["响应式特性"]
    },
    "user_flows": [
        {
            "name": "流程名称",
            "steps": ["步骤1", "步骤2"],
            "test_scenarios": ["测试场景1", "测试场景2"]
        }
    ],
    "test_recommendations": {
        "functional_tests": ["功能测试建议"],
        "ui_tests": ["界面测试建议"],
        "compatibility_tests": ["兼容性测试建议"],
        "edge_cases": ["边界情况测试"]
    }
}
"""
        
        if ui_descriptions:
            prompt += f"""

**补充UI描述信息：**
{chr(10).join([f"- {desc}" for desc in ui_descriptions])}

请结合这些UI描述信息进行更准确的分析。
"""
        
        return prompt
    
    def _parse_analysis_result(self, analysis_text: str) -> Dict[str, Any]:
        """解析分析结果"""
        try:
            # 尝试提取JSON部分
            start_idx = analysis_text.find('{')
            end_idx = analysis_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = analysis_text[start_idx:end_idx]
                result = json.loads(json_str)
                
                # 添加原始分析文本
                result["raw_analysis"] = analysis_text
                return result
            else:
                # 如果没有找到JSON，返回基本结构
                return {
                    "ui_elements": [],
                    "page_layout": {"structure": "无法解析页面结构"},
                    "user_flows": [],
                    "test_recommendations": {
                        "functional_tests": [],
                        "ui_tests": [],
                        "compatibility_tests": [],
                        "edge_cases": []
                    },
                    "raw_analysis": analysis_text
                }
                
        except json.JSONDecodeError:
            # JSON解析失败，返回基本信息
            return {
                "ui_elements": [],
                "page_layout": {"structure": "JSON解析失败"},
                "user_flows": [],
                "test_recommendations": {
                    "functional_tests": [],
                    "ui_tests": [],
                    "compatibility_tests": [],
                    "edge_cases": []
                },
                "raw_analysis": analysis_text,
                "parse_error": True
            }
    
    def generate_test_cases_from_analysis(self, analysis_result: Dict[str, Any], requirement_text: str) -> List[Dict[str, Any]]:
        """基于图片分析结果生成测试用例"""
        test_cases = []
        
        # 基于UI元素生成测试用例
        for element in analysis_result.get("ui_elements", []):
            for test_point in element.get("test_points", []):
                test_case = {
                    "title": f"{element['name']} - {test_point}",
                    "description": f"测试{element['name']}的{test_point}功能",
                    "category": "UI测试",
                    "priority": "Medium",
                    "ui_element": element['name'],
                    "test_type": "functional"
                }
                test_cases.append(test_case)
        
        # 基于用户流程生成测试用例
        for flow in analysis_result.get("user_flows", []):
            for scenario in flow.get("test_scenarios", []):
                test_case = {
                    "title": f"{flow['name']} - {scenario}",
                    "description": f"测试{flow['name']}流程中的{scenario}",
                    "category": "流程测试",
                    "priority": "High",
                    "user_flow": flow['name'],
                    "test_type": "functional"
                }
                test_cases.append(test_case)
        
        return test_cases
    
    def extract_ui_elements_summary(self, analysis_result: Dict[str, Any]) -> str:
        """提取UI元素摘要，用于测试用例生成"""
        if not analysis_result or analysis_result.get("error"):
            return "无法分析页面截图"
        
        summary_parts = []
        
        # UI元素摘要
        ui_elements = analysis_result.get("ui_elements", [])
        if ui_elements:
            element_types = {}
            for element in ui_elements:
                elem_type = element.get("type", "unknown")
                if elem_type not in element_types:
                    element_types[elem_type] = []
                element_types[elem_type].append(element.get("name", "未命名"))
            
            summary_parts.append("**页面UI元素：**")
            for elem_type, names in element_types.items():
                summary_parts.append(f"- {elem_type}: {', '.join(names[:3])}{'...' if len(names) > 3 else ''}")
        
        # 用户流程摘要
        user_flows = analysis_result.get("user_flows", [])
        if user_flows:
            summary_parts.append("\n**主要用户流程：**")
            for flow in user_flows[:3]:  # 最多显示3个流程
                summary_parts.append(f"- {flow.get('name', '未命名流程')}")
        
        # 测试建议摘要
        test_recommendations = analysis_result.get("test_recommendations", {})
        if test_recommendations:
            summary_parts.append("\n**测试重点：**")
            for category, recommendations in test_recommendations.items():
                if recommendations:
                    summary_parts.append(f"- {category}: {len(recommendations)}个测试点")
        
        return "\n".join(summary_parts) if summary_parts else "页面分析完成，包含多个UI元素和交互功能"
