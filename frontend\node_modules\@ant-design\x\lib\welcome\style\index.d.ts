/// <reference types="react" />
import type { FullToken, GetDefaultToken } from '../../theme/cssinjs-utils';
export interface ComponentToken {
}
export interface WelcomeToken extends FullToken<'Welcome'> {
}
export declare const prepareComponentToken: GetDefaultToken<'Welcome'>;
declare const _default: (prefixCls: string, rootCls?: string | undefined) => readonly [(node: import("react").ReactElement<unknown, string | import("react").JSXElementConstructor<any>>) => import("react").ReactElement<unknown, string | import("react").JSXElementConstructor<any>>, string, string];
export default _default;
